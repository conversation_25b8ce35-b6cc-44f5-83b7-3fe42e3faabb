# 🎯 Voice Enhanced RNNoise 音频测试工具

## 📋 概述
这是一个基于训练好的RNN模型的语音增强和降噪工具，专门用于处理WAV格式的音频文件。

## 📁 文件说明

### 🎯 主要文件
- **`VoiceEnhancedRNNoise.exe`** - 主要的可执行文件（位于 `dist/` 目录）
- **`test_audio.bat`** - 便捷的批处理脚本，简化使用流程
- **`voice_enhanced_test.py`** - Python源代码（如果需要修改）

### 🤖 训练好的模型
- **`best_models/best_voice_enhanced_model.keras`** - 最佳模型（推荐使用）
- **`final_weights/voice_enhanced_rnnoise_final.keras`** - 最终训练模型
- **`checkpoints/voice-enhanced-XXX-XXX.keras`** - 各轮次的检查点模型

## 🚀 使用方法

### 方法一：使用批处理脚本（推荐）

```batch
# 基本用法
test_audio.bat input.wav

# 指定输出文件
test_audio.bat input.wav output.wav

# 指定特定模型
test_audio.bat input.wav output.wav final_weights/voice_enhanced_rnnoise_final.keras
```

### 方法二：直接使用可执行文件

```batch
# 基本用法
dist\VoiceEnhancedRNNoise.exe input.wav

# 指定输出文件
dist\VoiceEnhancedRNNoise.exe input.wav -o output.wav

# 指定模型文件
dist\VoiceEnhancedRNNoise.exe input.wav -o output.wav -m best_models/best_voice_enhanced_model.keras
```

## 📝 参数说明

### 必需参数
- **输入文件** - WAV格式的音频文件路径

### 可选参数
- **`-o, --output`** - 输出文件路径（默认：`input_enhanced.wav`）
- **`-m, --model`** - 模型文件路径（默认：`best_models/best_voice_enhanced_model.keras`）

## 🎵 音频要求

- **格式**: WAV
- **采样率**: 16kHz（工具会自动转换）
- **声道**: 单声道或立体声（会转换为单声道处理）
- **位深**: 16位或24位

## 🔧 模型特点

### 🎯 专门优化
- **68维输入特征** (38维基础 + 30维人声增强特征)
- **37维输出** (18维降噪 + 18维人声增强 + 1维VAD)
- **人声频段优化** (300-3400Hz)
- **75.5%权重专注人声增强**

### 📈 训练效果
- **训练轮数**: 120轮完整训练
- **损失下降**: 99.6% (从6319.77降至27.77)
- **专用架构**: 独立人声处理分支和GRU层

## 💡 使用示例

### 示例1：基本降噪
```batch
test_audio.bat noisy_speech.wav
# 输出：noisy_speech_enhanced.wav
```

### 示例2：会议录音处理
```batch
test_audio.bat meeting_recording.wav clean_meeting.wav
# 输出：clean_meeting.wav
```

### 示例3：使用特定模型
```batch
test_audio.bat podcast.wav enhanced_podcast.wav checkpoints/voice-enhanced-100-50.12345.keras
# 使用第100轮训练的模型
```

## 🔍 处理流程

1. **音频加载** - 自动转换为16kHz单声道
2. **特征提取** - 提取68维增强特征
3. **模型推理** - 使用训练好的RNN模型
4. **音频重建** - 应用频谱掩码重建音频
5. **结果保存** - 输出处理后的WAV文件

## ⚠️ 注意事项

- 确保输入文件是有效的WAV格式
- 处理时间取决于音频长度和计算机性能
- 建议在安静环境下测试效果
- 模型专门针对人声优化，对音乐效果可能有限

## 🐛 故障排除

### 常见问题

**Q: 提示"模型文件不存在"**
A: 检查模型文件路径是否正确，确保使用相对路径

**Q: 音频处理失败**
A: 确认输入文件是有效的WAV格式，尝试使用音频编辑软件重新保存

**Q: 输出音频质量不佳**
A: 尝试使用不同的模型文件，或检查输入音频质量

**Q: 程序运行缓慢**
A: 这是正常现象，深度学习推理需要时间，请耐心等待

## 📊 性能参考

- **短音频** (< 30秒): 通常几秒内完成
- **中等音频** (1-5分钟): 可能需要30秒-2分钟
- **长音频** (> 5分钟): 处理时间与音频长度成正比

## 🎉 享受清晰的音频！

使用这个工具，您可以：
- ✅ 去除背景噪音
- ✅ 增强人声清晰度
- ✅ 改善录音质量
- ✅ 处理会议录音
- ✅ 优化播客音频

---
*由 Voice Enhanced RNNoise 提供技术支持*
