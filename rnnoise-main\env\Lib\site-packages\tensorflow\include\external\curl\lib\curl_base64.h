#ifndef HEADER_CURL_BASE64_H
#define HEADER_CURL_BASE64_H
/***************************************************************************
 *                                  _   _ ____  _
 *  Project                     ___| | | |  _ \| |
 *                             / __| | | | |_) | |
 *                            | (__| |_| |  _ <| |___
 *                             \___|\___/|_| \_\_____|
 *
 * Copyright (C) <PERSON>, <<EMAIL>>, et al.
 *
 * This software is licensed as described in the file COPYING, which
 * you should have received as part of this distribution. The terms
 * are also available at https://curl.se/docs/copyright.html.
 *
 * You may opt to use, copy, modify, merge, publish, distribute and/or sell
 * copies of the Software, and permit persons to whom the Software is
 * furnished to do so, under the terms of the COPYING file.
 *
 * This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
 * KIND, either express or implied.
 *
 * SPDX-License-Identifier: curl
 *
 ***************************************************************************/

#ifndef BUILDING_LIBCURL
/* this renames functions so that the tool code can use the same code
   without getting symbol collisions */
#define Curl_base64_encode(a,b,c,d) curlx_base64_encode(a,b,c,d)
#define Curl_base64url_encode(a,b,c,d) curlx_base64url_encode(a,b,c,d)
#define Curl_base64_decode(a,b,c) curlx_base64_decode(a,b,c)
#endif

CURLcode Curl_base64_encode(const char *inputbuff, size_t insize,
                            char **outptr, size_t *outlen);
CURLcode Curl_base64url_encode(const char *inputbuff, size_t insize,
                               char **outptr, size_t *outlen);
CURLcode Curl_base64_decode(const char *src,
                            unsigned char **outptr, size_t *outlen);
#endif /* HEADER_CURL_BASE64_H */
