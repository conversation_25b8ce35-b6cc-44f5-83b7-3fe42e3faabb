@echo off
chcp 65001 >nul
echo ================================================================
echo 🎯 Voice Enhanced RNNoise 演示程序
echo ================================================================
echo.
echo 这是一个语音增强和降噪工具的演示程序
echo 使用训练好的深度学习模型处理WAV音频文件
echo.
echo 功能特点:
echo   ✓ 专门的人声增强算法
echo   ✓ 智能噪声抑制
echo   ✓ 68维特征提取
echo   ✓ 120轮深度训练
echo   ✓ 99.6%%损失下降率
echo.
echo ================================================================
echo 📁 可用的模型文件:
echo ================================================================

if exist "best_models\best_voice_enhanced_model.keras" (
    echo   🏆 best_models\best_voice_enhanced_model.keras ^(推荐 - 最佳验证性能^)
)

if exist "final_weights\voice_enhanced_rnnoise_final.keras" (
    echo   🎯 final_weights\voice_enhanced_rnnoise_final.keras ^(最终训练结果^)
)

echo.
echo   📊 检查点模型 ^(训练过程中的各个阶段^):
for /f %%i in ('dir /b checkpoints\voice-enhanced-*.keras 2^>nul ^| head -3') do (
    echo      - checkpoints\%%i
)
echo      ... ^(共120个检查点^)

echo.
echo ================================================================
echo 🚀 使用方法:
echo ================================================================
echo.
echo 方法1: 使用便捷脚本
echo   test_audio.bat your_audio.wav
echo.
echo 方法2: 直接使用可执行文件
echo   dist\VoiceEnhancedRNNoise.exe your_audio.wav
echo.
echo 方法3: 指定输出文件和模型
echo   dist\VoiceEnhancedRNNoise.exe input.wav -o output.wav -m model.keras
echo.
echo ================================================================
echo 📝 示例命令:
echo ================================================================
echo.
echo # 基本用法 ^(使用最佳模型^)
echo test_audio.bat noisy_speech.wav
echo.
echo # 指定输出文件名
echo test_audio.bat meeting.wav clean_meeting.wav
echo.
echo # 使用特定的检查点模型
echo test_audio.bat podcast.wav enhanced.wav checkpoints\voice-enhanced-100-45.67890.keras
echo.
echo ================================================================
echo ⚠️  重要提示:
echo ================================================================
echo.
echo • 支持的格式: WAV ^(16kHz采样率最佳^)
echo • 处理时间: 取决于音频长度，请耐心等待
echo • 最佳效果: 人声录音、会议音频、播客等
echo • 模型大小: 约400MB ^(包含完整的深度学习模型^)
echo.
echo ================================================================
echo 🎵 开始处理您的音频文件吧！
echo ================================================================
echo.

REM 检查是否有WAV文件作为参数
if "%~1" NEQ "" (
    if exist "%~1" (
        echo 检测到输入文件: %~1
        echo 开始处理...
        echo.
        call test_audio.bat "%~1"
    ) else (
        echo ❌ 文件不存在: %~1
    )
) else (
    echo 💡 提示: 您可以将WAV文件拖拽到此批处理文件上来快速处理
    echo 或者手动运行上述命令
)

echo.
pause
