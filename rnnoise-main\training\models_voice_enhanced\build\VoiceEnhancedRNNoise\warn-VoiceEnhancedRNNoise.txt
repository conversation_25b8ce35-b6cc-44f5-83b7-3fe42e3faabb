
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named usercustomize - imported by site (top-level)
missing module named sitecustomize - imported by site (top-level)
excluded module named __main__ - imported by bdb (top-level), pdb (top-level), rlcompleter (top-level), profile (top-level), cProfile (top-level)
missing module named 'org.python' - imported by copy (top-level), xml.sax (top-level)
missing module named org - imported by pickle (top-level)
missing module named pwd - imported by posixpath (top-level), shutil (top-level), tarfile (top-level), pathlib (top-level), subprocess (top-level), http.server (top-level), webbrowser (top-level), netrc (top-level), getpass (top-level), distutils.util (top-level), setuptools._vendor.backports.tarfile (optional), distutils.archive_util (top-level), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (top-level), tarfile (top-level), pathlib (top-level), subprocess (top-level), setuptools._vendor.backports.tarfile (optional), distutils.archive_util (top-level), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (top-level), posixpath (top-level), shutil (top-level), importlib._bootstrap_external (top-level)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (top-level), importlib (top-level), importlib.abc (top-level), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (top-level), importlib.abc (top-level), zipimport (top-level)
missing module named readline - imported by cmd (top-level), code (top-level), pdb (top-level), site (top-level), rlcompleter (top-level), pstats (top-level), tensorflow.python.debug.cli.readline_ui (top-level)
missing module named termios - imported by tty (top-level), getpass (top-level), absl.flags._helpers (optional)
missing module named _posixsubprocess - imported by subprocess (top-level), multiprocessing.util (top-level), joblib.externals.loky.backend.fork_exec (delayed)
missing module named fcntl - imported by subprocess (top-level), absl.flags._helpers (optional), pty (top-level)
missing module named vms_lib - imported by platform (top-level)
missing module named 'java.lang' - imported by platform (top-level), xml.sax._exceptions (top-level)
missing module named java - imported by platform (top-level)
missing module named _winreg - imported by platform (top-level), pygments.formatters.img (optional)
missing module named pyimod02_importers - imported by D:\RNN\rnnoise-main\training\env\python\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), D:\RNN\rnnoise-main\training\env\python\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (top-level), multiprocessing.shared_memory (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), joblib.externals.loky.backend.context (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), joblib.parallel (top-level), numba.testing.main (optional)
missing module named _scproxy - imported by urllib.request (top-level)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.freeze_support - imported by multiprocessing (conditional), numba.runtests (conditional)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (top-level), asyncio.events (top-level)
missing module named annotationlib - imported by typing_extensions (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional), scipy._lib.array_api_compat.common._typing (conditional), sklearn.externals.array_api_compat.common._typing (conditional), setuptools._distutils.dist (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named 'Cython.Distutils' - imported by setuptools.command.build_ext (conditional, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional)
missing module named StringIO - imported by six (conditional)
missing module named six.moves.zip - imported by six.moves (top-level), pasta.base.annotate (top-level)
runtime module named six.moves - imported by astunparse (top-level), tensorflow.python.distribute.multi_process_runner (top-level), tensorflow.python.distribute.coordinator.cluster_coordinator (top-level), six.moves.urllib (top-level), pasta.base.annotate (top-level)
missing module named six.moves.cStringIO - imported by six.moves (top-level), astunparse (top-level)
missing module named rules_python - imported by tensorflow.python.platform.resource_loader (optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named astn - imported by gast.ast2 (top-level)
missing module named theano - imported by opt_einsum.backends.theano (delayed)
missing module named jax - imported by optree.integrations.jax (top-level), opt_einsum.backends.jax (delayed, conditional), keras.src.trainers.data_adapters.data_adapter_utils (delayed), keras.src.backend.jax.core (top-level), keras.src.backend.jax.distribution_lib (top-level), keras.src.backend.jax.image (top-level), keras.src.backend.jax.linalg (top-level), keras.src.backend.jax.math (top-level), keras.src.backend.jax.nn (top-level), keras.src.backend.jax.random (top-level), keras.src.backend.jax.rnn (top-level), keras.src.backend.jax.optimizer (top-level), keras.src.backend.jax.trainer (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib._array_api (delayed, conditional), scipy._lib.array_api_extra._lib._utils._helpers (delayed, conditional), scipy._lib.array_api_extra._lib._lazy (delayed, conditional), keras.src.backend.numpy.nn (top-level), keras.src.backend.jax.export (delayed), keras.src.ops.nn (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_extra._lib._lazy (delayed, conditional), tensorflow.lite.python.util (optional)
missing module named cupy - imported by opt_einsum.backends.cupy (delayed), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.cupy (top-level), sklearn.externals.array_api_compat.cupy._aliases (top-level), sklearn.externals.array_api_compat.cupy._info (top-level), sklearn.externals.array_api_compat.cupy._typing (top-level), sklearn.externals.array_api_compat.common._helpers (delayed, conditional), sklearn.utils._testing (delayed, conditional), sklearn.externals.array_api_compat.cupy.fft (top-level), sklearn.externals.array_api_compat.cupy.linalg (top-level)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional), joblib.compressor (optional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named compression - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named chardet - imported by requests (optional), pygments.lexer (delayed, conditional, optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named oauth2client - imported by tensorflow.python.distribute.cluster_resolver.gce_cluster_resolver (optional), tensorflow.python.tpu.client.client (optional)
missing module named googleapiclient - imported by tensorflow.python.distribute.cluster_resolver.gce_cluster_resolver (optional), tensorflow.python.tpu.client.client (optional)
missing module named cloud_tpu_client - imported by tensorflow.python.distribute.cluster_resolver.tpu.tpu_cluster_resolver (optional)
missing module named kubernetes - imported by tensorflow.python.distribute.cluster_resolver.kubernetes_cluster_resolver (delayed, conditional, optional)
missing module named fsspec - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional)
missing module named boto3 - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional)
missing module named botocore - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional)
missing module named tensorboard.compat.notf - imported by tensorboard.compat (delayed, optional)
missing module named 'tensorflow.compat' - imported by keras.src.callbacks.tensorboard (delayed), tensorboard.util.op_evaluator (delayed), tensorboard.util.encoder (delayed), tensorboard.plugins.audio.summary (delayed), tensorboard.plugins.custom_scalar.summary (delayed), tensorboard.plugins.histogram.summary (delayed), tensorboard.plugins.image.summary (delayed), tensorboard.plugins.pr_curve.summary (delayed), tensorboard.plugins.scalar.summary (delayed), tensorboard.plugins.text.summary (delayed)
missing module named 'keras.optimizers.optimizer_v2' - imported by tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named 'IPython.utils' - imported by h5py.ipy_completer (top-level)
missing module named IPython - imported by rich.jupyter (delayed, optional), keras.src.utils.model_visualization (delayed, conditional, optional), h5py (delayed, conditional, optional), h5py.ipy_completer (top-level), keras.src.saving.file_editor (delayed, optional), tensorflow.python.keras.utils.vis_utils (delayed, conditional, optional)
missing module named 'IPython.core' - imported by rich.pretty (delayed, optional), h5py (delayed, conditional, optional), h5py.ipy_completer (top-level)
missing module named pytest - imported by scipy._lib._testutils (delayed), scipy._lib._array_api (delayed), scipy._lib.array_api_extra.testing (conditional), h5py.tests (delayed, optional), sklearn.utils._testing (optional), pooch (delayed)
missing module named mpi4py - imported by h5py._hl.files (delayed)
missing module named 'tensorflow.saved_model' - imported by keras.src.export.saved_model (delayed)
missing module named 'jax.experimental' - imported by keras.src.trainers.data_adapters.data_adapter_utils (delayed), keras.src.testing.test_case (delayed, conditional), keras.src.backend.jax.core (top-level), keras.src.backend.jax.numpy (top-level), keras.src.backend.jax.nn (top-level), keras.src.backend.jax.sparse (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), keras.src.backend.jax.export (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional)
missing module named onnx - imported by keras.src.export.tf2onnx_lib (delayed)
missing module named 'torch.utils' - imported by keras.src.trainers.data_adapters.data_adapter_utils (delayed), keras.src.trainers.data_adapters.grain_dataset_adapter (delayed), keras.src.utils.dataset_utils (delayed, conditional)
missing module named 'grain._src' - imported by keras.src.trainers.data_adapters.grain_dataset_adapter (delayed)
missing module named grain - imported by keras.src.trainers.data_adapters.grain_dataset_adapter (optional)
missing module named pandas - imported by keras.src.trainers.data_adapters.array_slicing (optional), sklearn.utils.fixes (optional), sklearn.utils._missing (delayed), sklearn.utils._mask (delayed), sklearn.utils._optional_dependencies (delayed, optional), sklearn.utils.validation (delayed, conditional), sklearn.utils._param_validation (delayed, optional), tensorflow.python.keras.engine.data_adapter (delayed, optional)
missing module named 'tensorflow.summary' - imported by keras.src.callbacks.tensorboard (delayed, conditional)
missing module named 'torch.nn' - imported by keras.src.utils.torch_utils (delayed), keras.src.callbacks.callback (delayed, conditional), keras.src.backend.torch.image (top-level), keras.src.backend.torch.nn (top-level), keras.src.backend.torch.random (top-level), keras.src.ops.nn (delayed, conditional)
missing module named huggingface_hub - imported by keras.src.saving.saving_lib (optional)
missing module named psutil - imported by numpy.testing._private.utils (delayed, optional), scipy._lib._testutils (delayed, optional), keras.src.saving.saving_lib (optional), joblib.externals.loky.backend.context (delayed, optional), joblib.externals.loky.backend.utils (optional), joblib.externals.loky.process_executor (optional)
missing module named flax - imported by keras.src.backend.jax.core (conditional), keras.src.backend.jax.trainer (conditional), keras.src.backend.jax.layer (conditional), keras.src.ops.operation (delayed, conditional), keras.src.backend.config (delayed, conditional, optional)
missing module named keras.src.ops.convert_to_tensor - imported by keras.src.ops (top-level), keras.src.utils.torch_utils (top-level)
missing module named keras.src.ops.convert_to_numpy - imported by keras.src.ops (top-level), keras.src.utils.torch_utils (top-level)
missing module named 'tf_keras.optimizers' - imported by tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named tf_keras - imported by tensorflow.python.util.lazy_loader (delayed, conditional, optional), tensorflow.python.saved_model.load (delayed, conditional, optional)
missing module named objgraph - imported by tensorflow.python.distribute.test_util (optional)
missing module named tblib - imported by tensorflow.python.distribute.multi_process_runner (optional)
missing module named dill - imported by tensorflow.python.distribute.multi_process_runner (optional)
missing module named tensorflow.python.framework.fast_tensor_util - imported by tensorflow.python.framework (optional), tensorflow.python.framework.tensor_util (optional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), cffi.lock (conditional, optional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named win32pdh - imported by numpy.testing._private.utils (delayed, conditional)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named portpicker - imported by tensorflow.python.framework.test_util (delayed), tensorflow.dtensor.python.tests.multi_client_test_util (top-level), tensorflow.python.debug.lib.grpc_debug_test_server (top-level)
missing module named 'tensorflow.python.framework.is_mlir_bridge_test_true' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.framework.is_mlir_bridge_test_false' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'tensorflow.python.framework.is_xla_test_true' - imported by tensorflow.python.framework.test_util (optional)
missing module named 'six.moves.urllib.request' - imported by tensorflow.python.keras.utils.data_utils (top-level)
missing module named tensorflow.python.keras.__version__ - imported by tensorflow.python.keras (delayed), tensorflow.python.keras.saving.saving_utils (delayed), tensorflow.python.keras.saving.hdf5_format (delayed), tensorflow.python.keras.engine.training (delayed)
missing module named scipy.sparse.issparse - imported by scipy.sparse (top-level), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._milp (top-level), scipy.linalg._sketches (top-level), sklearn.utils._param_validation (top-level), sklearn.externals._scipy.sparse.csgraph._laplacian (top-level), sklearn.utils._set_output (top-level), sklearn.utils.multiclass (top-level), sklearn.metrics.cluster._unsupervised (top-level), sklearn.metrics.pairwise (top-level), sklearn.metrics._pairwise_distances_reduction._dispatcher (top-level), sklearn.cluster._feature_agglomeration (top-level), sklearn.cluster._bicluster (top-level), sklearn.neighbors._base (top-level), sklearn.decomposition._pca (top-level), sklearn.cluster._hdbscan.hdbscan (top-level), sklearn.cluster._optics (top-level), sklearn.manifold._isomap (top-level), sklearn.manifold._t_sne (top-level), sklearn.metrics._classification (top-level), sklearn.metrics._ranking (top-level), sklearn.utils._indexing (top-level), tensorflow.python.keras.engine.data_adapter (delayed, optional), tensorflow.python.keras.engine.training_arrays_v1 (optional), tensorflow.python.keras.engine.training_v1 (optional), sklearn.tree._classes (top-level), scipy.sparse.csgraph._validation (top-level)
missing module named 'jax.numpy' - imported by keras.src.backend.jax.core (top-level), keras.src.backend.jax.image (top-level), keras.src.backend.jax.linalg (top-level), keras.src.backend.jax.math (top-level), keras.src.backend.jax.numpy (top-level), keras.src.backend.jax.nn (top-level), keras.src.backend.jax.sparse (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_extra.testing (delayed), sklearn.externals.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.dask.array (top-level), sklearn.externals.array_api_compat.dask.array._aliases (top-level), sklearn.externals.array_api_compat.dask.array.fft (top-level), sklearn.externals.array_api_compat.dask.array.linalg (top-level)
missing module named sparse - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (conditional), sklearn.externals.array_api_compat.common._helpers (conditional)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (conditional), scipy._lib.array_api_extra._lib._lazy (delayed, conditional), scipy._lib.array_api_extra.testing (delayed), joblib._dask (optional), sklearn.externals.array_api_extra._lib._lazy (delayed, conditional)
missing module named scipy._lib.array_api_compat.common.array_namespace - imported by scipy._lib.array_api_compat.common (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level), sklearn.externals.array_api_compat.cupy._typing (top-level)
missing module named Cython - imported by scipy._lib._testutils (optional)
missing module named cython - imported by scipy._lib._testutils (optional)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named 'dask.typing' - imported by scipy._lib.array_api_extra.testing (conditional)
missing module named array_api_compat - imported by scipy._lib.array_api_extra._lib._utils._compat (optional), sklearn.externals.array_api_extra._lib._utils._compat (optional)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.special.comb - imported by scipy.special (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._rbfinterp (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._resampling (top-level), scipy.linalg._special_matrices (delayed), sklearn.model_selection._split (top-level), sklearn.preprocessing._polynomial (top-level), scipy.signal._filter_design (top-level)
missing module named scipy.special.logit - imported by scipy.special (top-level), sklearn._loss.link (top-level)
missing module named scipy.special.expit - imported by scipy.special (top-level), sklearn.gaussian_process._gpc (top-level), sklearn.linear_model._base (top-level), sklearn._loss.link (top-level)
missing module named scipy.special.erf - imported by scipy.special (top-level), sklearn.gaussian_process._gpc (top-level)
missing module named scipy.special.inv_boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named scipy.special.boxcox - imported by scipy.special (top-level), sklearn.preprocessing._data (top-level)
missing module named 'matplotlib.pyplot' - imported by scipy.stats._fit (delayed, conditional), scipy.stats._survival (delayed, conditional), scipy.stats._distribution_infrastructure (delayed, optional), keras.src.visualization.plot_image_gallery (optional), sklearn.utils._plotting (delayed), sklearn.model_selection._plot (delayed), sklearn.metrics._plot.confusion_matrix (delayed), sklearn.metrics._plot.regression (delayed), librosa.display (top-level), sklearn.tree._export (delayed)
missing module named matplotlib - imported by scipy.spatial._plotutils (delayed), scipy.stats._fit (delayed, optional), scipy.stats._survival (delayed, optional), keras.src.visualization.plot_bounding_box_gallery (optional), sklearn.utils._optional_dependencies (delayed, optional), keras.src.saving.file_editor (delayed, optional), librosa.display (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named 'matplotlib.ticker' - imported by scipy.stats._fit (delayed), librosa.display (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named 'matplotlib.collections' - imported by scipy.spatial._plotutils (delayed), scipy.cluster.hierarchy (delayed, optional), librosa.display (conditional)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named scipy.optimize.minimize - imported by scipy.optimize (delayed, conditional, optional), scipy._lib.pyprima.common._project (delayed, conditional, optional), scipy.optimize._differentialevolution (top-level), scipy.optimize._shgo (top-level), scipy.optimize._dual_annealing (top-level), sklearn.neighbors._nca (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named scipy.special.betainc - imported by scipy.special (top-level), scipy.stats._quantile (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.stats._multivariate (top-level), scipy.fft._fftlog_backend (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.psi - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.gammainc - imported by scipy.special (top-level), scipy.stats._qmc (top-level), sklearn.neighbors._kde (top-level)
missing module named scipy.special.ndtri - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._binomtest (top-level), scipy.stats._relative_risk (top-level), scipy.stats._odds_ratio (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ndtr - imported by scipy.special (top-level), scipy.stats._resampling (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.kv - imported by scipy.special (top-level), scipy.stats._hypotests (top-level), sklearn.gaussian_process.kernels (top-level)
missing module named scipy.special.gamma - imported by scipy.special (top-level), scipy.stats._hypotests (top-level), sklearn.gaussian_process.kernels (top-level)
missing module named scipy.special.zeta - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.gammaln - imported by scipy.special (top-level), scipy.integrate._quadrature (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._hypotests (top-level), scipy.stats._multivariate (top-level), scipy.optimize._dual_annealing (top-level), scipy.special._spfun_stats (top-level), sklearn.decomposition._lda (top-level)
missing module named scipy.special.roots_legendre - imported by scipy.special (top-level), scipy.integrate._quadrature (top-level), scipy.integrate._rules._gauss_legendre (top-level)
missing module named scipy.special.entr - imported by scipy.special (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.xlogy - imported by scipy.special (top-level), scipy.interpolate._rbf (top-level), scipy.stats._multivariate (top-level), sklearn._loss.loss (top-level), sklearn.metrics._classification (top-level)
missing module named scipy.special.factorial - imported by scipy.special (top-level), scipy.interpolate._polyint (top-level), scipy.stats._resampling (top-level)
missing module named scipy.special.poch - imported by scipy.special (top-level), scipy.interpolate._bsplines (top-level), scipy.fft._fftlog_backend (top-level)
missing module named scipy.special.rel_entr - imported by scipy.special (top-level), scipy.spatial.distance (top-level)
missing module named scipy.linalg.cho_solve_banded - imported by scipy.linalg (top-level), scipy.interpolate._bsplines (top-level)
missing module named scipy.linalg.cholesky_banded - imported by scipy.linalg (top-level), scipy.interpolate._bsplines (top-level)
missing module named scipy.linalg.solve_banded - imported by scipy.linalg (top-level), scipy.spatial.transform._rotation_spline (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._cubic (top-level)
missing module named scipy.linalg.orthogonal_procrustes - imported by scipy.linalg (top-level), scipy.spatial._procrustes (top-level)
missing module named scipy.linalg.solve_triangular - imported by scipy.linalg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.linalg._matfuncs_inv_ssq (top-level), sklearn.gaussian_process._gpr (top-level)
missing module named scipy.linalg.cho_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._trustregion_exact (top-level), scipy.optimize._lsq.common (top-level), sklearn.gaussian_process._gpc (top-level), sklearn.gaussian_process._gpr (top-level)
missing module named scipy.linalg.cho_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._lsq.common (top-level)
missing module named scipy.linalg.inv - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._nonlin (top-level)
missing module named scipy.linalg.lu_solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.linalg.lu_factor - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.linalg.eigh - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy._lib.cobyqa.models (top-level), sklearn.decomposition._kernel_pca (top-level), sklearn.manifold._locally_linear (top-level), sklearn.manifold._spectral_embedding (top-level)
missing module named scipy.linalg.eig - imported by scipy.linalg (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level)
missing module named scipy.linalg.lstsq - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.signal._fir_filter_design (top-level), scipy.signal._savitzky_golay (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.linalg.svd - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.sparse.linalg._eigen._svds (top-level), scipy.optimize._minpack_py (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._remove_redundancy (top-level), scipy.linalg._decomp_polar (top-level), sklearn.cluster._spectral (top-level), sklearn.manifold._locally_linear (top-level)
missing module named scipy.linalg.solve - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy.optimize._nonlin (top-level), scipy.optimize._linprog_rs (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._cubic (top-level), sklearn.gaussian_process._gpc (top-level), sklearn.manifold._locally_linear (top-level), scipy.signal._fir_filter_design (top-level)
missing module named scipy.linalg.qr - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level), scipy._lib.cobyqa.subsolvers.optim (top-level), scipy.optimize._lsq.trf (top-level), scipy.optimize._lsq.trf_linear (top-level), scipy.optimize._nonlin (top-level), sklearn.cluster._spectral (top-level), sklearn.manifold._locally_linear (top-level), scipy.signal._ltisys (top-level)
missing module named scipy.linalg.cholesky - imported by scipy.linalg (top-level), scipy.optimize._optimize (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.optimize._minpack_py (top-level), sklearn.gaussian_process._gpc (top-level), sklearn.gaussian_process._gpr (top-level)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), sklearn.manifold._locally_linear (top-level)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._milp (top-level)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), sklearn.cluster._optics (top-level)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named yaml - imported by numpy.__config__ (delayed), scipy.__config__ (delayed), numba.core.config (optional), numba.tests.support (delayed)
missing module named tensorflow.python.keras.layers.wrappers - imported by tensorflow.python.keras.layers (delayed), tensorflow.python.keras.utils.vis_utils (delayed)
missing module named 'jax.typing' - imported by optree.integrations.jax (top-level)
missing module named 'jax._src' - imported by optree.integrations.jax (top-level), keras.src.backend.jax.nn (delayed, optional)
missing module named 'openvino.runtime' - imported by keras.src.backend.openvino.core (top-level), keras.src.backend.openvino.math (top-level), keras.src.backend.openvino.nn (top-level), keras.src.backend.openvino.numpy (top-level), keras.src.backend.openvino.random (top-level), keras.src.backend.openvino.trainer (top-level), keras.src.export.openvino (delayed)
missing module named openvino - imported by keras.src.backend.openvino.core (top-level), keras.src.backend.openvino.math (top-level), keras.src.backend.openvino.nn (top-level), keras.src.backend.openvino.numpy (top-level), keras.src.backend.openvino.random (top-level), keras.src.backend.openvino.trainer (top-level), keras.src.export.openvino (delayed)
missing module named 'jax.nn' - imported by keras.src.backend.jax.nn (delayed, optional)
missing module named 'torch._dynamo' - imported by keras.src.backend.torch.image (top-level), keras.src.backend.torch.random (top-level), keras.src.backend.common.global_state (delayed, conditional)
missing module named 'torch.backends' - imported by keras.src.backend.torch.nn (delayed, optional)
missing module named 'jax.scipy' - imported by keras.src.backend.jax.linalg (top-level)
missing module named 'tensorflow.experimental' - imported by keras.src.backend.tensorflow.distribution_lib (top-level)
missing module named torch - imported by optree.integrations.torch (top-level), opt_einsum.backends.torch (delayed, conditional), keras.src.backend.torch.core (top-level), keras.src.testing.test_case (delayed, conditional), keras.src.backend.torch.optimizers.torch_optimizer (top-level), keras.src.utils.torch_utils (delayed), keras.src.backend.torch.optimizers.torch_adadelta (top-level), keras.src.backend.torch.optimizers.torch_parallel_optimizer (top-level), keras.src.backend.torch.optimizers.torch_adagrad (top-level), keras.src.backend.torch.optimizers.torch_adam (top-level), keras.src.backend.torch.optimizers.torch_adamax (top-level), keras.src.backend.torch.optimizers.torch_lion (top-level), keras.src.backend.torch.optimizers.torch_nadam (top-level), keras.src.backend.torch.optimizers.torch_rmsprop (top-level), keras.src.backend.torch.optimizers.torch_sgd (top-level), keras.src.utils.rng_utils (delayed, conditional), keras.src.backend.torch.trainer (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.torch (top-level), scipy._lib.array_api_compat.torch._aliases (top-level), scipy._lib.array_api_compat.torch._info (top-level), scipy._lib.array_api_compat.torch._typing (top-level), keras.src.backend.torch.export (top-level), keras.src.export.onnx (delayed, conditional), keras.src.export.openvino (delayed, conditional), keras.src.backend.torch.layer (top-level), keras.src.backend.torch.image (top-level), keras.src.backend.torch.linalg (top-level), keras.src.backend.torch.math (top-level), keras.src.backend.torch.numpy (top-level), keras.src.backend.torch.nn (top-level), keras.src.backend.torch.random (top-level), keras.src.backend.torch.rnn (top-level), keras.src.trainers.data_adapters.array_data_adapter (delayed), keras.src.trainers.data_adapters.torch_data_loader_adapter (delayed), keras.src.backend (conditional), sklearn.externals.array_api_compat.common._helpers (delayed, conditional), sklearn.externals.array_api_compat.torch (top-level), sklearn.externals.array_api_compat.torch._aliases (top-level), sklearn.externals.array_api_compat.torch._info (top-level), sklearn.externals.array_api_compat.torch._typing (top-level), sklearn.externals.array_api_compat.torch.fft (top-level), sklearn.externals.array_api_compat.torch.linalg (top-level)
missing module named keras.src.backend.random_seed_dtype - imported by keras.src.backend (delayed), keras.src.random.seed_generator (delayed)
missing module named keras.src.backend.convert_to_tensor - imported by keras.src.backend (delayed), keras.src.random.seed_generator (delayed)
missing module named keras.src.backend.random - imported by keras.src.backend (top-level), keras.src.ops (top-level), keras.src.testing.test_case (delayed), keras.src.initializers.random_initializers (top-level)
missing module named keras.src.backend.is_tensor - imported by keras.src.backend (top-level), keras.src.ops (top-level)
missing module named keras.src.backend.cond - imported by keras.src.backend (top-level), keras.src.ops (top-level)
missing module named keras.src.backend.cast - imported by keras.src.backend (top-level), keras.src.ops (top-level)
missing module named pydotplus - imported by keras.src.utils.model_visualization (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named pydot_ng - imported by keras.src.utils.model_visualization (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named pydot - imported by keras.src.utils.model_visualization (optional), tensorflow.python.keras.utils.vis_utils (optional)
missing module named PIL - imported by keras.src.utils.image_utils (optional), keras.src.legacy.preprocessing.image (delayed), pygments.formatters.img (optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named pygments.formatters.TerminalFormatter - imported by pygments.formatters (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.formatters.Terminal256Formatter - imported by pygments.formatters (delayed, conditional, optional), numba.core.ir (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.lexers.LlvmLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments.lexers.GasLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional)
missing module named ipywidgets - imported by rich.live (delayed, conditional, optional)
missing module named 'IPython.display' - imported by rich.live (delayed, conditional, optional), llvmlite.binding.analysis (delayed, conditional, optional)
missing module named attr - imported by rich.pretty (optional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named cv2 - imported by keras.src.visualization.draw_bounding_boxes (optional)
missing module named 'flax.core' - imported by keras.src.utils.jax_layer (delayed)
missing module named 'pandas.api' - imported by sklearn.utils.validation (delayed, optional)
missing module named pyarrow - imported by sklearn.utils.fixes (optional), sklearn.utils._indexing (delayed, conditional)
missing module named sklearn.externals.array_api_compat.common.array_namespace - imported by sklearn.externals.array_api_compat.common (top-level), sklearn.externals.array_api_compat.dask.array._aliases (top-level)
missing module named 'torch.linalg' - imported by sklearn.externals.array_api_compat.torch.linalg (top-level)
missing module named 'torch.fft' - imported by sklearn.externals.array_api_compat.torch.fft (top-level)
missing module named 'cupy.linalg' - imported by sklearn.externals.array_api_compat.cupy.linalg (top-level)
missing module named 'cupy.fft' - imported by sklearn.externals.array_api_compat.cupy.fft (top-level)
missing module named numpy.VisibleDeprecationWarning - imported by numpy (conditional), sklearn.utils.fixes (conditional)
missing module named numpy.ComplexWarning - imported by numpy (conditional), sklearn.utils.fixes (conditional)
missing module named array_api_strict - imported by sklearn.utils._array_api (delayed, conditional, optional)
missing module named 'matplotlib.text' - imported by sklearn.tree._export (delayed)
missing module named pyamg - imported by sklearn.manifold._spectral_embedding (delayed, conditional, optional)
missing module named 'matplotlib.patches' - imported by scipy.cluster.hierarchy (delayed, optional)
missing module named 'matplotlib.pylab' - imported by scipy.cluster.hierarchy (delayed, conditional, optional)
missing module named 'numpydoc.docscrape' - imported by sklearn.utils._testing (delayed)
missing module named numpydoc - imported by sklearn.utils._testing (delayed, optional)
missing module named tornado - imported by joblib._dask (conditional, optional)
missing module named 'distributed.utils' - imported by joblib._dask (conditional, optional)
missing module named 'dask.utils' - imported by joblib._dask (conditional)
missing module named 'dask.sizeof' - imported by joblib._dask (conditional)
missing module named 'dask.distributed' - imported by joblib._dask (conditional)
missing module named distributed - imported by joblib._dask (optional), joblib._parallel_backends (delayed, optional)
missing module named viztracer - imported by joblib.externals.loky.initializers (delayed, optional)
missing module named numpy.byte_bounds - imported by numpy (delayed, optional), joblib._memmapping_reducer (delayed, optional)
missing module named 'lz4.frame' - imported by joblib.compressor (optional)
missing module named lz4 - imported by joblib.compressor (optional)
missing module named 'tensorflow.contrib' - imported by tensorflow.python.tools.import_pb_to_tensorboard (optional)
missing module named memory_profiler - imported by tensorflow.python.eager.memory_tests.memory_test_util (optional)
missing module named six.moves.urllib.request - imported by six.moves.urllib (top-level), tensorflow.python.distribute.failure_handling.failure_handling_util (top-level)
missing module named grpc_reflection - imported by grpc (optional)
missing module named grpc_health - imported by grpc (optional)
missing module named grpc_tools - imported by grpc._runtime_protos (delayed, optional), grpc (optional)
missing module named 'grpc_tools.protoc' - imported by grpc._runtime_protos (delayed, conditional)
missing module named tflite_runtime - imported by tensorflow.lite.python.metrics.metrics (conditional), tensorflow.lite.python.interpreter (conditional), tensorflow.lite.python.analyzer (conditional), tensorflow.lite.tools.visualize (conditional)
missing module named llvmlite.binding.parse_assembly - imported by llvmlite.binding (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Value - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Constant - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed), numba.core.base (top-level), numba.core.pythonapi (top-level), numba.core.lowering (top-level), numba.core.generators (top-level), numba.core.callwrapper (top-level), numba.pycc.llvm_types (top-level), numba.np.npdatetime (top-level), numba.np.math.mathimpl (top-level), numba.np.math.numbers (top-level), numba.cpython.unicode (top-level), numba.np.arrayobj (top-level), numba.np.ufunc.wrappers (top-level), numba.cpython.old_mathimpl (top-level), numba.cpython.old_numbers (top-level), numba.cpython.new_mathimpl (top-level), numba.cpython.new_numbers (top-level)
missing module named llvmlite.ir.GlobalVariable - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Module - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named numba.typed.List - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.core.codegen (delayed), numba.typed.listobject (delayed)
missing module named numba.typed.Dict - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.typed.dictobject (delayed, conditional), numba.typed.dictimpl (delayed)
missing module named numba.core.types.DictIteratorType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictValuesIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictKeysIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictItemsIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictType - imported by numba.core.types (top-level), numba.typed.typeddict (top-level), numba.typed.dictobject (top-level)
missing module named coverage - imported by numba.misc.coverage_support (optional), numba.tests.support (optional)
missing module named numba.core.types.WrapperAddressProtocol - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionPrototype - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.UndefinedFunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named 'win32com.shell' - imported by numba.misc.appdirs (conditional, optional)
missing module named 'com.sun' - imported by numba.misc.appdirs (delayed, conditional, optional)
missing module named com - imported by numba.misc.appdirs (delayed)
missing module named win32api - imported by numba.misc.appdirs (delayed, conditional, optional)
missing module named win32com - imported by numba.misc.appdirs (delayed)
missing module named numba.core.types.ClassInstanceType - imported by numba.core.types (top-level), numba.experimental.jitclass.overloads (top-level)
missing module named 'elftools.elf' - imported by numba.core.codegen (delayed)
missing module named elftools - imported by numba.core.codegen (delayed)
missing module named graphviz - imported by llvmlite.binding.analysis (delayed), numba.core.ir (delayed, optional), numba.core.controlflow (delayed, optional), numba.misc.inspection (delayed, optional), numba.core.codegen (delayed)
missing module named r2pipe - imported by numba.misc.inspection (delayed, optional)
missing module named jinja2 - imported by numba.core.annotations.type_annotations (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named numba.core.types.NoneType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.Type - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ListTypeIteratorType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListTypeIterableType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListType - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.typedlist (top-level)
missing module named numba.core.types.Tuple - imported by numba.core.types (delayed), numba.core.types.iterators (delayed), numba.core.types.npytypes (delayed)
missing module named numba.core.types.Array - imported by numba.core.types (delayed), numba.core.types.abstract (delayed)
missing module named numba.core.types.StringLiteral - imported by numba.core.types (top-level), numba.np.arrayobj (top-level)
missing module named xmlrunner - imported by numba.testing (delayed, conditional)
missing module named git - imported by numba.testing.main (delayed, optional)
missing module named ptxcompiler - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cubinlinker - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named numba.cuda.is_available - imported by numba.cuda (delayed), numba.cuda.testing (delayed)
missing module named cuda - imported by numba.core.config (delayed, conditional, optional), numba.cuda.cudadrv.driver (conditional)
missing module named Queue - imported by numba.testing.main (optional)
missing module named colorama - imported by numba.core.errors (optional)
missing module named numba.types.uint64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level)
missing module named numba.types.int64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float32 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.Tuple - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.void - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int32 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int16 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named paramiko - imported by pooch.downloaders (optional)
missing module named tqdm - imported by pooch.downloaders (optional)
missing module named xxhash - imported by pooch.hashes (optional)
missing module named '__pypy__.builders' - imported by msgpack.fallback (conditional)
missing module named __pypy__ - imported by msgpack.fallback (conditional)
missing module named scipy.signal.convolve - imported by scipy.signal (delayed, conditional), scipy.signal._polyutils (delayed, conditional)
missing module named librosa.util.expand_to - imported by librosa.util (top-level), librosa.sequence (top-level), librosa.feature.inverse (top-level)
missing module named librosa.util.nnls - imported by librosa.util (top-level), librosa.feature.inverse (top-level)
missing module named librosa.feature.tempo - imported by librosa.feature (top-level), librosa.beat (top-level)
missing module named librosa.feature.fourier_tempogram - imported by librosa.feature (top-level), librosa.beat (top-level)
missing module named 'matplotlib.colors' - imported by librosa.display (conditional)
missing module named 'matplotlib.markers' - imported by librosa.display (conditional)
missing module named 'matplotlib.path' - imported by librosa.display (conditional)
missing module named 'matplotlib.lines' - imported by librosa.display (conditional)
missing module named 'matplotlib.axes' - imported by librosa.display (top-level)
missing module named librosa.util.Deprecated - imported by librosa.util (top-level), librosa.core.pitch (top-level)
missing module named librosa.util.is_unique - imported by librosa.util (top-level), librosa.core.harmonic (top-level)
missing module named mad - imported by audioread (delayed, optional), audioread.maddec (top-level)
missing module named 'gi.repository' - imported by audioread (delayed, optional), audioread.gstdec (top-level)
missing module named gi - imported by audioread (delayed, optional), audioread.gstdec (top-level)
missing module named librosa.util.tiny - imported by librosa.util (top-level), librosa.sequence (top-level)
missing module named librosa.util.is_positive_int - imported by librosa.util (top-level), librosa.sequence (top-level)
missing module named librosa.util.fill_off_diagonal - imported by librosa.util (top-level), librosa.sequence (top-level)
missing module named librosa.util.pad_center - imported by librosa.util (top-level), librosa.sequence (top-level)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
