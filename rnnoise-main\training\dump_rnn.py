#!/usr/bin/python

from __future__ import print_function

from keras.models import Sequential
from keras.layers import Dense
from keras.layers import LSTM
from keras.layers import GRU
from keras.models import load_model
from keras import backend as K
from keras.constraints import Constraint
import keras
import sys
import re
import numpy as np

# 启用不安全的反序列化
keras.config.enable_unsafe_deserialization()



def printVector(f, vector, name):
    v = np.reshape(vector, (-1));
    #print('static const float ', name, '[', len(v), '] = \n', file=f)
    f.write('static const rnn_weight {}[{}] = {{\n   '.format(name, len(v)))
    for i in range(0, len(v)):
        f.write('{}'.format(min(127, int(round(256*v[i])))))
        if (i!=len(v)-1):
            f.write(',')
        else:
            break;
        if (i%8==7):
            f.write("\n   ")
        else:
            f.write(" ")
    #print(v, file=f)
    f.write('\n};\n\n')
    return;

def printCompatibleInputLayer(f, hf, layer):
    """处理68维到38维的输入层权重适配"""
    weights = layer.get_weights()
    W = weights[0]  # 权重矩阵 (68, 32)
    b = weights[1]  # 偏置向量 (32,)

    # 只使用前38维的权重，丢弃人声增强特征部分
    W_compatible = W[:38, :]  # (38, 32)

    printVector(f, W_compatible, layer.name + '_weights')
    printVector(f, b, layer.name + '_bias')

    name = layer.name
    activation = re.search('function (.*) at', str(layer.activation)).group(1).upper()
    f.write('const DenseLayer {} = {{\n   {}_bias,\n   {}_weights,\n   {}, ACTIVATION_{}\n}};\n\n'
            .format(name, name, name, layer.units, activation))

    print(f"Converted input layer: 68-dim -> 38-dim compatible")

def printLayer(f, hf, layer):
    weights = layer.get_weights()
    printVector(f, weights[0], layer.name + '_weights')
    if len(weights) > 2:
        printVector(f, weights[1], layer.name + '_recurrent_weights')
    printVector(f, weights[-1], layer.name + '_bias')
    name = layer.name
    activation = re.search('function (.*) at', str(layer.activation)).group(1).upper()
    if len(weights) > 2:
        f.write('const GRULayer {} = {{\n   {}_bias,\n   {}_weights,\n   {}_recurrent_weights,\n   {}, {}, ACTIVATION_{}\n}};\n\n'
                .format(name, name, name, name, weights[0].shape[0], weights[0].shape[1]/3, activation))
        hf.write('#define {}_SIZE {}\n'.format(name.upper(), weights[0].shape[1]/3))
        hf.write('extern const GRULayer {};\n\n'.format(name));
    else:
        f.write('const DenseLayer {} = {{\n   {}_bias,\n   {}_weights,\n   {}, {}, ACTIVATION_{}\n}};\n\n'
                .format(name, name, name, weights[0].shape[0], weights[0].shape[1], activation))
        hf.write('#define {}_SIZE {}\n'.format(name.upper(), weights[0].shape[1]))
        hf.write('extern const DenseLayer {};\n\n'.format(name));


#def foo(c, name):
#    return 1

#def mean_squared_sqrt_error(y_true, y_pred):
#    return K.mean(K.square(K.sqrt(y_pred) - K.sqrt(y_true)), axis=-1)

def mean_squared_sqrt_error(y_true, y_pred):
    return K.mean(K.square(K.sqrt(y_pred) - K.sqrt(y_true)), axis=-1)

def my_crossentropy(y_true, y_pred):
    """适配VAD输出的交叉熵损失函数"""
    return K.binary_crossentropy(y_true, y_pred)

def human_voice_enhancement_loss(y_true, y_pred):
    """专门的人声增强损失函数 - 只关注人声特有特征"""
    # 人声频段权重映射 (18个频段对应不同的人声特征重要性)
    voice_frequency_weights = K.constant([
        # 低频段 (80-300Hz) - 基频区域，权重适中
        1.5, 1.8, 2.0,
        # 中频段 (300-1000Hz) - 人声核心频段，最高权重
        3.0, 3.5, 4.0, 3.8, 3.2,
        # 中高频段 (1000-3400Hz) - 重要谐波，高权重
        2.8, 2.5, 2.2, 2.0, 1.8,
        # 高频段 (3400-8000Hz) - 清晰度相关，中等权重
        1.5, 1.2, 1.0, 0.8, 0.6
    ])

    # 人声增强专用损失计算
    harmonic_loss = K.square(y_pred - y_true) * voice_frequency_weights
    clarity_loss = K.square(K.log(y_pred + 1e-8) - K.log(y_true + 1e-8)) * voice_frequency_weights * 0.5
    voice_mask = K.cast(y_true > 0.1, K.floatx())
    continuity_loss = K.square(y_pred - y_true) * voice_mask * 0.3
    dynamic_loss = K.abs(y_pred - y_true) * voice_frequency_weights * 0.2

    total_voice_loss = harmonic_loss + clarity_loss + continuity_loss + dynamic_loss
    return K.mean(total_voice_loss, axis=-1)

def human_voice_loss(y_true, y_pred):
    """专门的人声增强损失函数"""
    return K.mean(K.square(y_pred - y_true), axis=-1)

def mymask(y_true):
    return K.minimum(y_true+1., 1.)

def msse(y_true, y_pred):
    return K.mean(mymask(y_true)*K.square(K.sqrt(y_pred)-K.sqrt(y_true)),axis=-1)

def mycost(y_true, y_pred):
    return K.mean(mymask(y_true) * (10*K.square(K.square(K.sqrt(y_pred) - K.sqrt(y_true))) + K.square(K.sqrt(y_pred) - K.sqrt(y_true)) + 0.01*K.binary_crossentropy(y_pred, y_true)), axis=-1)

def my_accuracy(y_true, y_pred):
    return K.mean(2*K.abs(y_true-0.5) * K.equal(y_true, K.round(y_pred)), axis=-1)

class WeightClip(Constraint):
    def __init__(self, c=2, name='WeightClip'):
        self.c = c

    def __call__(self, p):
        return K.clip(p, -self.c, self.c)

    def get_config(self):
        return {'c': self.c}

    @classmethod
    def from_config(cls, config):
        return cls(c=config.get('c', 2))




if __name__ == '__main__':
   # model = load_model(sys.argv[1], {'msse': mean_squared_sqrt_error,
   #                                  'mean_squared_sqrt_error': mean_squared_sqrt_error,
   #                                  'my_crossentropy': mean_squared_sqrt_error,
   #                                  'mycost': mean_squared_sqrt_error,
   #                                  'WeightClip': foo})

    model = load_model(
            sys.argv[1],
            custom_objects={
                'msse':msse,
                'mean_squared_sqrt_error': mean_squared_sqrt_error,
                'my_crossentropy':my_crossentropy,
                'mycost':mycost,
                'my_accuracy':my_accuracy,
                'human_voice_loss':human_voice_loss,
                'human_voice_enhancement_loss':human_voice_enhancement_loss,
                'WeightClip':WeightClip}
            )
    weights = model.get_weights()

    f = open(sys.argv[2], 'w')
    hf = open(sys.argv[3], 'w')

    f.write('/*This file is automatically generated from a Keras model*/\n\n')
    f.write('#ifdef HAVE_CONFIG_H\n#include "config.h"\n#endif\n\n#include "rnn.h"\n\n')

    hf.write('/*This file is automatically generated from a Keras model*/\n\n')
    hf.write('#ifndef RNN_DATA_H\n#define RNN_DATA_H\n\n#include "rnn.h"\n\n')

    layer_list = []
    for i, layer in enumerate(model.layers):
        if len(layer.get_weights()) > 0:
            # 只保留兼容原始RNNoise的核心层
            if layer.name in ['voice_output', 'vad_output', 'voice_enhancement_output', 'voice_specific']:
                print(f"Skipping layer: {layer.name}")
                continue

            # 对于input_dense层，需要适配68维到38维的权重
            if layer.name == 'input_dense':
                printCompatibleInputLayer(f, hf, layer)
            else:
                printLayer(f, hf, layer)
        if len(layer.get_weights()) > 2:
            layer_list.append(layer.name)

    hf.write('struct RNNState {\n')
    for i, name in enumerate(layer_list):
        hf.write('  float {}_state[{}_SIZE];\n'.format(name, name.upper()))
    hf.write('};\n')

    hf.write('\n\n#endif\n')

    f.close()
    hf.close()
