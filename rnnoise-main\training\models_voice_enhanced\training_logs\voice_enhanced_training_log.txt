Voice Enhanced RNNoise Training Completed
Date: 2025-08-05 18:00:32.156885
=== SPECIALIZED VOICE ENHANCEMENT CONFIGURATION ===
Input dimensions: 68 (38 original + 30 voice enhancement features)
Output dimensions: 18 noise suppression + 18 voice enhancement + 1 VAD
Loss functions: mycost + human_voice_enhancement_loss + my_crossentropy
Loss weights: [6, 20, 0.5] (Noise:Voice:VAD) - 75.5% focus on voice
Architecture: Specialized voice processing branch with dedicated GRU
Voice features: Separate processing of 30-dim voice features
Voice loss: Frequency-weighted for 300-3400Hz human voice range
Training sequences: 380
Batch size: 16
Epochs: 120
Final model: models_voice_enhanced/final_weights/voice_enhanced_rnnoise_final.keras
Best model: models_voice_enhanced/best_models/best_voice_enhanced_model.keras
