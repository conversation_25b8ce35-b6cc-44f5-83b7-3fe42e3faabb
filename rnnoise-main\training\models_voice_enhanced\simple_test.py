#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的音频测试脚本 - 使用原始RNNoise方法
"""

import os
import sys
import numpy as np
import librosa
import soundfile as sf
import argparse

def extract_rnnoise_features(audio, sr=16000, frame_size=480, hop_size=160):
    """提取RNNoise风格的特征"""
    # 确保音频长度
    if len(audio) < frame_size:
        audio = np.pad(audio, (0, frame_size - len(audio)))
    
    # 计算帧数
    n_frames = (len(audio) - frame_size) // hop_size + 1
    features = []
    
    for i in range(n_frames):
        start = i * hop_size
        end = start + frame_size
        frame = audio[start:end]
        
        # 计算STFT
        stft = librosa.stft(frame, n_fft=512, hop_length=160)
        magnitude = np.abs(stft)
        power_spectrum = magnitude ** 2
        
        # 18个频段的能量 (对应RNNoise的频段)
        freq_bands = np.array_split(power_spectrum, 18, axis=0)
        band_energies = [np.mean(band) for band in freq_bands]
        
        # 基本特征
        zcr = librosa.feature.zero_crossing_rate(frame)[0, 0]
        spectral_centroid = librosa.feature.spectral_centroid(y=frame, sr=sr)[0, 0]
        rms = librosa.feature.rms(y=frame)[0, 0]
        
        # 组合特征 (22维，接近原始RNNoise)
        frame_features = np.array(band_energies + [zcr, spectral_centroid, rms, np.mean(frame)])
        features.append(frame_features)
    
    return np.array(features)

def simple_noise_reduction(audio, sr=16000):
    """简单的噪声抑制算法"""
    # 计算STFT
    stft = librosa.stft(audio, n_fft=512, hop_length=160)
    magnitude = np.abs(stft)
    phase = np.angle(stft)
    
    # 简单的谱减法
    # 估计噪声谱 (使用前几帧作为噪声估计)
    noise_frames = 10
    noise_spectrum = np.mean(magnitude[:, :noise_frames], axis=1, keepdims=True)
    
    # 计算信噪比掩码
    snr = magnitude / (noise_spectrum + 1e-8)
    
    # Wiener滤波器
    wiener_gain = snr / (snr + 1)
    
    # 应用增益，保留更多人声
    enhanced_magnitude = magnitude * np.clip(wiener_gain, 0.1, 1.0)
    
    # 重建音频
    enhanced_stft = enhanced_magnitude * np.exp(1j * phase)
    enhanced_audio = librosa.istft(enhanced_stft, hop_length=160)
    
    return enhanced_audio

def voice_enhancement(audio, sr=16000):
    """人声增强处理"""
    # 分离谐波和冲击成分
    harmonic, percussive = librosa.effects.hpss(audio)
    
    # 增强谐波成分 (通常包含人声)
    enhanced_harmonic = harmonic * 1.2
    
    # 减少冲击成分 (通常是噪声)
    reduced_percussive = percussive * 0.3
    
    # 合并
    enhanced_audio = enhanced_harmonic + reduced_percussive
    
    # 应用简单的动态范围压缩
    enhanced_audio = np.tanh(enhanced_audio * 2) * 0.8
    
    return enhanced_audio

def process_audio_simple(input_path, output_path):
    """处理音频文件 - 简化版本"""
    try:
        # 加载音频
        audio, sr = librosa.load(input_path, sr=16000)
        print(f"✓ 加载音频: {input_path}")
        print(f"  - 采样率: {sr} Hz")
        print(f"  - 时长: {len(audio)/sr:.2f} 秒")
        
        # 第一步：噪声抑制
        print("正在进行噪声抑制...")
        denoised_audio = simple_noise_reduction(audio, sr)
        
        # 第二步：人声增强
        print("正在进行人声增强...")
        enhanced_audio = voice_enhancement(denoised_audio, sr)
        
        # 归一化
        enhanced_audio = enhanced_audio / np.max(np.abs(enhanced_audio)) * 0.95
        
        # 保存结果
        sf.write(output_path, enhanced_audio, sr)
        print(f"✓ 处理完成，输出保存到: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 处理音频失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Simple Voice Enhancement Tool')
    parser.add_argument('input', help='输入WAV文件路径')
    parser.add_argument('-o', '--output', help='输出WAV文件路径 (默认: input_simple_enhanced.wav)')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"✗ 输入文件不存在: {args.input}")
        return 1
    
    # 设置输出文件
    if args.output is None:
        from pathlib import Path
        input_path = Path(args.input)
        args.output = str(input_path.parent / f"{input_path.stem}_simple_enhanced{input_path.suffix}")
    
    print("=" * 60)
    print("🎯 Simple Voice Enhancement Tool")
    print("=" * 60)
    print(f"输入文件: {args.input}")
    print(f"输出文件: {args.output}")
    print("-" * 60)
    
    # 处理音频
    success = process_audio_simple(args.input, args.output)
    
    if success:
        print("=" * 60)
        print("🎉 音频处理完成！")
        print("=" * 60)
        return 0
    else:
        print("=" * 60)
        print("❌ 音频处理失败！")
        print("=" * 60)
        return 1

if __name__ == '__main__':
    sys.exit(main())
