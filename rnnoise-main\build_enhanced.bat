@echo off
echo ================================================================
echo 🔨 Building Enhanced RNNoise with Voice Enhancement
echo ================================================================
echo.

REM 设置编译器路径
set CC=env\mingw64\bin\gcc.exe
set CFLAGS=-Wall -W -O3 -g -ffast-math -DENHANCED=1 -Iinclude

REM 源文件列表
set CORE_SOURCES=src/kiss_fft.c src/celt_lpc.c src/pitch.c src/rnn.c src/denoise.c
set ENHANCED_SOURCES=src/rnn_data_enhanced.c
set MAIN_SOURCE=main.c

REM 输出文件
set OUTPUT=bin\rnnoise_enhanced.exe

echo 编译器: %CC%
echo 编译选项: %CFLAGS%
echo 输出文件: %OUTPUT%
echo.

echo 开始编译...
echo ----------------------------------------------------------------

REM 编译命令
%CC% %CFLAGS% -o %OUTPUT% %CORE_SOURCES% %ENHANCED_SOURCES% %MAIN_SOURCE% -lm

if errorlevel 1 (
    echo.
    echo ❌ 编译失败！
    pause
    exit /b 1
) else (
    echo.
    echo ================================================================
    echo 🎉 编译成功！
    echo ================================================================
    echo   输出文件: %OUTPUT%
    echo   特性: 增强版RNNoise，支持人声增强
    echo   使用方法: %OUTPUT% input.wav output.wav
    echo ================================================================
)

pause
