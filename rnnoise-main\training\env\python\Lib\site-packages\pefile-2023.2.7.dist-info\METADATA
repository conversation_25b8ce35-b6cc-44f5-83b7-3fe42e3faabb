Metadata-Version: 2.1
Name: pefile
Version: 2023.2.7
Summary: Python PE parsing module
Home-page: https://github.com/erocarrera/pefile
Download-URL: https://github.com/erocarrera/pefile/releases/download/v2023.2.7/pefile-2023.2.7.tar.gz
Author: <PERSON><PERSON>
Author-email: <EMAIL>
License: MIT
Keywords: pe,exe,dll,pefile,pecoff
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.6.0
License-File: LICENSE

pefile, Portable Executable reader module

All the PE file basic structures are available with their default names as
attributes of the instance returned.

Processed elements such as the import table are made available with lowercase
names, to differentiate them from the upper case basic structure names.

pefile has been tested against many edge cases such as corrupted and malformed
PEs as well as malware, which often attempts to abuse the format way beyond its
standard use. To the best of my knowledge most of the abuse is handled
gracefully.

Copyright (c) 2005-2023 <PERSON><PERSON> <<EMAIL>>
