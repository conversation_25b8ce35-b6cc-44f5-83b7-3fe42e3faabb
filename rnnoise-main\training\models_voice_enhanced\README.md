# 🎯 Enhanced RNNoise 语音增强可执行文件

## 📋 概述
这是基于训练好的深度学习模型生成的语音增强和降噪可执行文件，使用了120轮训练的增强权重。

## 📁 文件说明

### 🎯 主要文件
- **`rnnoise_enhanced.exe`** - 主要的可执行文件（使用训练好的增强权重）
- **`rnn_data_enhanced.c`** - 从Keras模型转换的C权重文件
- **`voice6_enhanced.wav`** - 测试输出示例

### 🤖 训练好的模型
- **`best_models/best_voice_enhanced_model.keras`** - 最佳模型（推荐）
- **`final_weights/voice_enhanced_rnnoise_final.keras`** - 最终训练模型
- **`checkpoints/`** - 120个训练检查点

## 🚀 使用方法

### 基本用法
```bash
rnnoise_enhanced.exe input.wav output.wav
```

### 示例
```bash
# 处理单个音频文件
rnnoise_enhanced.exe noisy_speech.wav clean_speech.wav

# 处理会议录音
rnnoise_enhanced.exe meeting_recording.wav enhanced_meeting.wav

# 处理播客音频
rnnoise_enhanced.exe podcast_raw.wav podcast_enhanced.wav
```

## 🎵 音频要求

- **格式**: WAV
- **采样率**: 16kHz（推荐）
- **声道**: 单声道或立体声
- **位深**: 16位或24位

## 🔧 模型特点

### 🎯 训练配置
- **训练轮数**: 120轮完整训练
- **损失下降**: 99.6% (从6319.77降至27.77)
- **输入特征**: 68维 (38维基础 + 30维人声增强特征)
- **输出维度**: 37维 (18维降噪 + 18维人声增强 + 1维VAD)

### 📈 增强特性
- **专门人声优化** (300-3400Hz频段)
- **智能噪声抑制**
- **谐波增强处理**
- **动态范围优化**
- **75.5%权重专注人声增强**

## 💡 使用场景

### ✅ 适用场景
- 会议录音降噪
- 播客音频增强
- 电话录音清理
- 语音识别预处理
- 在线教育音频优化

### ⚠️ 注意事项
- 主要针对人声优化，对音乐效果有限
- 处理时间取决于音频长度
- 建议在安静环境下测试效果

## 📊 性能参考

- **短音频** (< 30秒): 通常几秒内完成
- **中等音频** (1-5分钟): 可能需要10-30秒
- **长音频** (> 5分钟): 处理时间与音频长度成正比

## 🔍 技术细节

### 模型架构
- **输入层**: 68维特征向量
- **隐藏层**: 多层GRU网络
- **输出层**: 三分支输出（降噪+人声增强+VAD）
- **权重约束**: WeightClip约束防止过拟合

### 特征提取
- **基础特征**: MFCC、频谱能量、过零率等
- **人声特征**: 谐波分析、基频检测、人声频段能量
- **增强特征**: 专门的人声增强特征提取

## 🎉 使用示例

### 测试命令
```bash
# 使用提供的测试音频
rnnoise_enhanced.exe voice6.wav voice6_processed.wav
```

### 批处理脚本示例
```batch
@echo off
echo 正在处理音频文件...
rnnoise_enhanced.exe %1 %~n1_enhanced%~x1
echo 处理完成！输出文件: %~n1_enhanced%~x1
pause
```

## 📝 版本信息

- **版本**: Enhanced RNNoise v1.0
- **训练日期**: 2025-08-05
- **模型大小**: ~50MB (权重文件)
- **可执行文件大小**: ~527KB

## 🔧 故障排除

### 常见问题

**Q: 提示找不到输入文件**
A: 确保输入文件路径正确，支持相对路径和绝对路径

**Q: 输出音频质量不佳**
A: 检查输入音频质量，确保采样率为16kHz

**Q: 处理速度慢**
A: 这是正常现象，深度学习推理需要时间

**Q: 程序崩溃**
A: 确保输入文件是有效的WAV格式

## 🎯 技术支持

如需技术支持或有问题反馈，请检查：
1. 输入文件格式是否正确
2. 文件路径是否存在
3. 系统是否有足够的内存和存储空间

---
*基于Enhanced RNNoise深度学习模型 - 专业语音增强解决方案*
