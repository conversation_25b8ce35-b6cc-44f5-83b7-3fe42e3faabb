#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Voice Enhanced RNNoise Audio Testing Tool
使用训练好的模型对WAV音频文件进行降噪和人声增强处理
"""

import os
import sys
import numpy as np
import librosa
import soundfile as sf
import tensorflow as tf
from keras.models import load_model
from keras import backend as K
from keras.constraints import Constraint
import argparse
from pathlib import Path

# 设置CPU运行
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'

def human_voice_enhancement_loss(y_true, y_pred):
    """专门的人声增强损失函数 - 只关注人声特有特征"""
    voice_frequency_weights = tf.constant([
        1.5, 1.8, 2.0, 3.0, 3.5, 4.0, 3.8, 3.2,
        2.8, 2.5, 2.2, 2.0, 1.8, 1.5, 1.2, 1.0, 0.8, 0.6
    ], dtype=tf.float32)
    
    harmonic_loss = tf.square(y_pred - y_true) * voice_frequency_weights
    clarity_loss = tf.square(tf.math.log(y_pred + 1e-8) - tf.math.log(y_true + 1e-8)) * voice_frequency_weights * 0.5
    voice_mask = tf.cast(y_true > 0.1, tf.float32)
    continuity_loss = tf.square(y_pred - y_true) * voice_mask * 0.3
    dynamic_loss = tf.abs(y_pred - y_true) * voice_frequency_weights * 0.2
    
    total_voice_loss = harmonic_loss + clarity_loss + continuity_loss + dynamic_loss
    return tf.reduce_mean(total_voice_loss, axis=-1)

def my_crossentropy(y_true, y_pred):
    """适配VAD输出的交叉熵损失函数"""
    return tf.keras.losses.binary_crossentropy(y_true, y_pred)

def mymask(y_true):
    return tf.minimum(y_true+1., 1.)

def msse(y_true, y_pred):
    return tf.reduce_mean(mymask(y_true) * tf.square(tf.sqrt(y_pred) - tf.sqrt(y_true)), axis=-1)

def mycost(y_true, y_pred):
    mask = mymask(y_true)
    sqrt_diff = tf.sqrt(tf.maximum(y_pred, 1e-8)) - tf.sqrt(tf.maximum(y_true, 1e-8))
    mse_loss = tf.square(sqrt_diff)
    quartic_loss = tf.square(mse_loss)
    total_loss = mask * (10 * quartic_loss + mse_loss)
    return tf.reduce_mean(total_loss, axis=-1)

def my_accuracy(y_true, y_pred):
    return tf.reduce_mean(2*tf.abs(y_true-0.5) * tf.cast(tf.equal(y_true, tf.round(y_pred)), tf.float32), axis=-1)

class WeightClip(Constraint):
    def __init__(self, c=2, **kwargs):
        super(WeightClip, self).__init__(**kwargs)
        self.c = c
    def __call__(self, p):
        return tf.clip_by_value(p, -self.c, self.c)
    def get_config(self):
        config = super(WeightClip, self).get_config()
        config.update({'c': self.c})
        return config

def extract_features(audio, sr=16000, frame_size=480, hop_size=160):
    """提取68维特征 (38维原始 + 30维人声增强特征)"""
    # 确保音频长度
    if len(audio) < frame_size:
        audio = np.pad(audio, (0, frame_size - len(audio)))
    
    # 计算帧数
    n_frames = (len(audio) - frame_size) // hop_size + 1
    features = []
    
    for i in range(n_frames):
        start = i * hop_size
        end = start + frame_size
        frame = audio[start:end]
        
        # 基础特征 (38维)
        # MFCC特征
        mfcc = librosa.feature.mfcc(y=frame, sr=sr, n_mfcc=13)
        mfcc_mean = np.mean(mfcc, axis=1)
        
        # 频谱特征
        stft = librosa.stft(frame, n_fft=512, hop_length=160)
        magnitude = np.abs(stft)
        power_spectrum = magnitude ** 2
        
        # 18个频段的能量
        freq_bands = np.array_split(power_spectrum, 18, axis=0)
        band_energies = [np.mean(band) for band in freq_bands]
        
        # 其他特征
        zcr = librosa.feature.zero_crossing_rate(frame)[0, 0]
        spectral_centroid = librosa.feature.spectral_centroid(y=frame, sr=sr)[0, 0]
        spectral_rolloff = librosa.feature.spectral_rolloff(y=frame, sr=sr)[0, 0]
        spectral_bandwidth = librosa.feature.spectral_bandwidth(y=frame, sr=sr)[0, 0]
        rms = librosa.feature.rms(y=frame)[0, 0]
        
        basic_features = np.concatenate([
            mfcc_mean,  # 13维
            band_energies,  # 18维
            [zcr, spectral_centroid, spectral_rolloff, spectral_bandwidth, rms, np.mean(frame), np.std(frame)]  # 7维
        ])
        
        # 人声增强特征 (30维)
        # 谐波特征
        harmonic, percussive = librosa.effects.hpss(frame)
        harmonic_energy = np.mean(harmonic ** 2)
        percussive_energy = np.mean(percussive ** 2)
        
        # 人声频段特征 (300-3400Hz)
        voice_freq_start = int(300 * len(magnitude) / (sr/2))
        voice_freq_end = int(3400 * len(magnitude) / (sr/2))
        voice_spectrum = magnitude[voice_freq_start:voice_freq_end]
        voice_bands = np.array_split(voice_spectrum, 20, axis=0)
        voice_energies = [np.mean(band) for band in voice_bands]
        
        # 基频相关特征
        f0 = librosa.yin(frame, fmin=80, fmax=400, sr=sr)
        f0_mean = np.mean(f0[f0 > 0]) if np.any(f0 > 0) else 0
        
        # 其他人声特征
        chroma = librosa.feature.chroma_stft(y=frame, sr=sr)
        chroma_mean = np.mean(chroma, axis=1)
        
        voice_features = np.concatenate([
            [harmonic_energy, percussive_energy, f0_mean],  # 3维
            voice_energies,  # 20维
            chroma_mean[:7]  # 7维，总共30维
        ])
        
        # 合并特征 (68维)
        frame_features = np.concatenate([basic_features, voice_features])
        features.append(frame_features)
    
    return np.array(features)

def load_voice_enhanced_model(model_path):
    """加载训练好的人声增强模型"""
    custom_objects = {
        'msse': msse,
        'my_crossentropy': my_crossentropy,
        'mycost': mycost,
        'my_accuracy': my_accuracy,
        'human_voice_enhancement_loss': human_voice_enhancement_loss,
        'WeightClip': WeightClip
    }
    
    try:
        model = load_model(model_path, custom_objects=custom_objects)
        print(f"✓ 成功加载模型: {model_path}")
        return model
    except Exception as e:
        print(f"✗ 加载模型失败: {e}")
        return None

def process_audio(model, audio_path, output_path):
    """处理音频文件"""
    try:
        # 加载音频
        audio, sr = librosa.load(audio_path, sr=16000)
        print(f"✓ 加载音频: {audio_path}")
        print(f"  - 采样率: {sr} Hz")
        print(f"  - 时长: {len(audio)/sr:.2f} 秒")
        
        # 提取特征
        print("正在提取特征...")
        features = extract_features(audio, sr)
        print(f"✓ 特征提取完成: {features.shape}")
        
        # 预处理特征
        features = features.reshape(1, features.shape[0], features.shape[1])
        
        # 模型推理
        print("正在进行模型推理...")
        predictions = model.predict(features, verbose=0)
        
        # 解析输出
        if isinstance(predictions, list):
            denoise_output = predictions[0][0]  # 噪声抑制输出
            voice_output = predictions[1][0]    # 人声增强输出
            vad_output = predictions[2][0]      # VAD输出
        else:
            denoise_output = predictions[0]
        
        print(f"✓ 推理完成")
        print(f"  - 降噪输出形状: {denoise_output.shape}")
        if isinstance(predictions, list):
            print(f"  - 人声增强输出形状: {voice_output.shape}")
            print(f"  - VAD输出形状: {vad_output.shape}")
        
        # 简单的音频重建 (这里使用基本的频谱掩码方法)
        enhanced_audio = reconstruct_audio(audio, denoise_output, sr)
        
        # 保存结果
        sf.write(output_path, enhanced_audio, sr)
        print(f"✓ 处理完成，输出保存到: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ 处理音频失败: {e}")
        return False

def reconstruct_audio(original_audio, mask_output, sr=16000):
    """使用掩码重建音频"""
    # 计算STFT
    stft = librosa.stft(original_audio, n_fft=512, hop_length=160)
    magnitude = np.abs(stft)
    phase = np.angle(stft)
    
    # 应用掩码 (简化处理)
    if len(mask_output) > 0:
        # 将18维掩码扩展到频谱维度
        freq_bins = magnitude.shape[0]
        mask_expanded = np.repeat(mask_output.reshape(-1, 1), freq_bins // 18 + 1, axis=1)[:, :freq_bins]
        mask_expanded = mask_expanded.T
        
        # 确保掩码和频谱维度匹配
        if mask_expanded.shape[1] > magnitude.shape[1]:
            mask_expanded = mask_expanded[:, :magnitude.shape[1]]
        elif mask_expanded.shape[1] < magnitude.shape[1]:
            mask_expanded = np.pad(mask_expanded, ((0, 0), (0, magnitude.shape[1] - mask_expanded.shape[1])), mode='edge')
        
        # 应用掩码
        enhanced_magnitude = magnitude * np.clip(mask_expanded, 0.1, 1.0)
    else:
        enhanced_magnitude = magnitude
    
    # 重建音频
    enhanced_stft = enhanced_magnitude * np.exp(1j * phase)
    enhanced_audio = librosa.istft(enhanced_stft, hop_length=160)
    
    return enhanced_audio

def main():
    parser = argparse.ArgumentParser(description='Voice Enhanced RNNoise Audio Testing Tool')
    parser.add_argument('input', help='输入WAV文件路径')
    parser.add_argument('-o', '--output', help='输出WAV文件路径 (默认: input_enhanced.wav)')
    parser.add_argument('-m', '--model', default='best_models/best_voice_enhanced_model.keras', 
                       help='模型文件路径 (默认: best_models/best_voice_enhanced_model.keras)')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"✗ 输入文件不存在: {args.input}")
        return 1
    
    # 设置输出文件
    if args.output is None:
        input_path = Path(args.input)
        args.output = str(input_path.parent / f"{input_path.stem}_enhanced{input_path.suffix}")
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"✗ 模型文件不存在: {args.model}")
        return 1
    
    print("=" * 60)
    print("🎯 Voice Enhanced RNNoise Audio Testing Tool")
    print("=" * 60)
    print(f"输入文件: {args.input}")
    print(f"输出文件: {args.output}")
    print(f"模型文件: {args.model}")
    print("-" * 60)
    
    # 加载模型
    model = load_voice_enhanced_model(args.model)
    if model is None:
        return 1
    
    # 处理音频
    success = process_audio(model, args.input, args.output)
    
    if success:
        print("=" * 60)
        print("🎉 音频处理完成！")
        print("=" * 60)
        return 0
    else:
        print("=" * 60)
        print("❌ 音频处理失败！")
        print("=" * 60)
        return 1

if __name__ == '__main__':
    sys.exit(main())
