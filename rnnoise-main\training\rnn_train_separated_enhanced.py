#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分离式训练脚本 - 降噪和人声增强分开训练
重点突出人声增强效果
"""

import os
import sys
import numpy as np
import h5py
import tensorflow as tf
from keras.models import Model, Sequential, load_model
from keras.layers import Dense, LSTM, GRU, Input, Lambda, Concatenate, Dropout, BatchNormalization
from keras.optimizers import Adam
from keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau
from keras import backend as K
from keras.constraints import Constraint
import datetime

# 设置随机种子
np.random.seed(42)
tf.random.set_seed(42)

class WeightClip(Constraint):
    def __init__(self, c=2, **kwargs):
        super(WeightClip, self).__init__(**kwargs)
        self.c = c
    def __call__(self, p):
        return tf.clip_by_value(p, -self.c, self.c)
    def get_config(self):
        config = super(WeightClip, self).get_config()
        config.update({'c': self.c})
        return config

def advanced_voice_enhancement_loss(y_true, y_pred):
    """
    高级人声增强损失函数 - 重点突出人声特征
    专门针对人声频段和谐波结构优化
    """
    # 人声频段权重 - 更加突出人声核心频段
    voice_frequency_weights = tf.constant([
        # 80-200Hz: 基频区域 - 中等权重
        2.0, 2.5, 3.0,
        # 200-500Hz: 人声基频核心 - 最高权重
        5.0, 6.0, 7.0, 6.5, 6.0,
        # 500-1500Hz: 人声主要谐波 - 很高权重
        5.5, 5.0, 4.5, 4.0, 3.8,
        # 1500-3500Hz: 清晰度关键频段 - 高权重
        3.5, 3.2, 3.0, 2.8, 2.5
    ], dtype=tf.float32)
    
    # 1. 谐波增强损失 - 突出人声谐波结构
    harmonic_loss = tf.square(y_pred - y_true) * voice_frequency_weights * 3.0
    
    # 2. 对数域损失 - 增强动态范围感知
    log_loss = tf.square(tf.math.log(y_pred + 1e-6) - tf.math.log(y_true + 1e-6)) * voice_frequency_weights * 2.0
    
    # 3. 人声掩码损失 - 只关注有人声的部分
    voice_mask = tf.cast(y_true > 0.15, tf.float32)  # 提高阈值，更严格筛选人声
    masked_loss = tf.square(y_pred - y_true) * voice_mask * voice_frequency_weights * 4.0
    
    # 4. 频谱连续性损失 - 保持人声的平滑性
    continuity_loss = tf.square(y_pred[..., 1:] - y_pred[..., :-1]) * 0.5
    continuity_loss = tf.pad(continuity_loss, [[0, 0], [0, 0], [0, 1]], mode='CONSTANT')
    
    # 5. 人声清晰度损失 - 增强高频细节
    clarity_weights = tf.constant([1.0, 1.2, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0, 
                                  5.5, 6.0, 6.5, 7.0, 7.5, 8.0, 8.5, 9.0], dtype=tf.float32)
    clarity_loss = tf.abs(y_pred - y_true) * clarity_weights * 2.5
    
    # 6. 动态对比度损失 - 增强人声与背景的对比
    dynamic_range = tf.reduce_max(y_true, axis=-1, keepdims=True) - tf.reduce_min(y_true, axis=-1, keepdims=True)
    contrast_loss = tf.square(y_pred - y_true) * (dynamic_range + 1.0) * 1.5
    
    # 综合损失 - 重点突出人声增强
    total_loss = (harmonic_loss * 0.3 + 
                  log_loss * 0.25 + 
                  masked_loss * 0.25 + 
                  continuity_loss * 0.05 + 
                  clarity_loss * 0.1 + 
                  contrast_loss * 0.05)
    
    return tf.reduce_mean(total_loss, axis=-1)

def denoise_loss(y_true, y_pred):
    """标准降噪损失函数"""
    return tf.reduce_mean(tf.square(tf.sqrt(tf.maximum(y_pred, 1e-8)) - tf.sqrt(tf.maximum(y_true, 1e-8))), axis=-1)

def vad_loss(y_true, y_pred):
    """VAD损失函数"""
    return tf.keras.losses.binary_crossentropy(y_true, y_pred)

def create_separated_voice_enhancement_model():
    """
    创建分离式人声增强模型
    降噪和人声增强使用不同的网络分支
    """
    # 输入层 - 68维增强特征
    input_layer = Input(shape=(None, 68), name='input_features')
    
    # 共享特征提取层
    shared_features = GRU(128, return_sequences=True, name='shared_gru_1')(input_layer)
    shared_features = Dropout(0.1)(shared_features)
    shared_features = GRU(128, return_sequences=True, name='shared_gru_2')(shared_features)
    
    # === 降噪分支 (保持原有效果) ===
    denoise_branch = GRU(96, return_sequences=True, name='denoise_gru_1')(shared_features)
    denoise_branch = Dropout(0.1)(denoise_branch)
    denoise_branch = GRU(96, return_sequences=True, name='denoise_gru_2')(denoise_branch)
    denoise_branch = Dense(64, activation='relu', name='denoise_dense_1')(denoise_branch)
    denoise_output = Dense(18, activation='sigmoid', name='denoise_output')(denoise_branch)
    
    # === 人声增强分支 (重点优化) ===
    # 第一层：人声特征提取
    voice_branch = GRU(160, return_sequences=True, name='voice_gru_1', 
                      kernel_constraint=WeightClip(2))(shared_features)
    voice_branch = BatchNormalization(name='voice_bn_1')(voice_branch)
    voice_branch = Dropout(0.05)(voice_branch)  # 较小的dropout保持人声细节
    
    # 第二层：人声谐波增强
    voice_branch = GRU(160, return_sequences=True, name='voice_gru_2',
                      kernel_constraint=WeightClip(2))(voice_branch)
    voice_branch = BatchNormalization(name='voice_bn_2')(voice_branch)
    voice_branch = Dropout(0.05)(voice_branch)
    
    # 第三层：人声清晰度优化
    voice_branch = GRU(128, return_sequences=True, name='voice_gru_3',
                      kernel_constraint=WeightClip(2))(voice_branch)
    voice_branch = BatchNormalization(name='voice_bn_3')(voice_branch)
    
    # 人声增强专用全连接层
    voice_branch = Dense(96, activation='relu', name='voice_dense_1',
                        kernel_constraint=WeightClip(2))(voice_branch)
    voice_branch = Dropout(0.05)(voice_branch)
    
    voice_branch = Dense(64, activation='relu', name='voice_dense_2',
                        kernel_constraint=WeightClip(2))(voice_branch)
    voice_branch = Dropout(0.05)(voice_branch)
    
    # 人声增强输出层 - 使用tanh激活增强动态范围
    voice_output = Dense(18, activation='tanh', name='voice_enhancement_output',
                        kernel_constraint=WeightClip(2))(voice_branch)
    # 将tanh输出转换为正值并增强
    voice_output = Lambda(lambda x: (x + 1.0) * 0.75 + 0.25, name='voice_enhancement_transform')(voice_output)
    
    # === VAD分支 ===
    vad_branch = GRU(64, return_sequences=True, name='vad_gru')(shared_features)
    vad_branch = Dense(32, activation='relu', name='vad_dense')(vad_branch)
    vad_output = Dense(1, activation='sigmoid', name='vad_output')(vad_branch)
    
    # 创建模型
    model = Model(inputs=input_layer, 
                  outputs=[denoise_output, voice_output, vad_output],
                  name='separated_voice_enhancement_model')
    
    return model

def compile_model_for_denoise_training(model):
    """为降噪训练编译模型"""
    model.compile(
        optimizer=Adam(learning_rate=0.001, beta_1=0.9, beta_2=0.999),
        loss={
            'denoise_output': denoise_loss,
            'voice_enhancement_transform': lambda y_true, y_pred: tf.zeros_like(y_true),  # 不训练人声增强
            'vad_output': vad_loss
        },
        loss_weights={
            'denoise_output': 0.8,
            'voice_enhancement_transform': 0.0,  # 降噪阶段不训练人声增强
            'vad_output': 0.2
        },
        metrics={
            'denoise_output': 'mse',
            'vad_output': 'accuracy'
        }
    )
    return model

def compile_model_for_voice_training(model):
    """为人声增强训练编译模型 - 重点优化"""
    model.compile(
        optimizer=Adam(learning_rate=0.0005, beta_1=0.9, beta_2=0.999),  # 较小学习率精细调整
        loss={
            'denoise_output': lambda y_true, y_pred: tf.zeros_like(y_true),  # 不训练降噪
            'voice_enhancement_transform': advanced_voice_enhancement_loss,  # 使用高级人声增强损失
            'vad_output': lambda y_true, y_pred: tf.zeros_like(y_true)  # 不训练VAD
        },
        loss_weights={
            'denoise_output': 0.0,  # 人声增强阶段不训练降噪
            'voice_enhancement_transform': 1.0,  # 全部权重给人声增强
            'vad_output': 0.0
        },
        metrics={
            'voice_enhancement_transform': 'mse'
        }
    )
    return model

def main():
    print("=" * 80)
    print("🎯 分离式人声增强训练 - 重点突出人声增强效果")
    print("=" * 80)
    
    # 加载训练数据
    print('Loading enhanced training data...')
    try:
        with h5py.File('training_data_enhanced.h5', 'r') as hf:
            all_data = hf['data'][:]
        print('✓ Training data loaded successfully.')
        print(f'  Data shape: {all_data.shape}')
    except FileNotFoundError:
        print('❌ Error: training_data_enhanced.h5 not found!')
        return 1
    
    # 数据预处理
    print('Preprocessing data...')
    print(f'  Original data shape: {all_data.shape}')

    # 重新整形数据为序列格式
    nb_samples = all_data.shape[0]
    sequence_length = 100  # 每个序列100帧
    nb_sequences = nb_samples // sequence_length

    # 截取能整除的部分
    usable_samples = nb_sequences * sequence_length
    all_data = all_data[:usable_samples]

    # 重新整形为 (sequences, frames, features)
    all_data = all_data.reshape(nb_sequences, sequence_length, -1)
    print(f'  Reshaped data: {all_data.shape}')

    # 输入特征 (68维)
    X = all_data[:, :, :68]

    # 输出目标
    Y_denoise = all_data[:, :, 68:86]      # 降噪目标 (18维)
    Y_voice = all_data[:, :, 86:104]       # 人声增强目标 (18维)
    Y_vad = all_data[:, :, 104:105]        # VAD目标 (1维)
    
    print(f'✓ Data preprocessing completed.')
    print(f'  Input features: {X.shape}')
    print(f'  Denoise targets: {Y_denoise.shape}')
    print(f'  Voice enhancement targets: {Y_voice.shape}')
    print(f'  VAD targets: {Y_vad.shape}')
    
    # 创建模型
    print('Creating separated voice enhancement model...')
    model = create_separated_voice_enhancement_model()
    print('✓ Model created successfully.')
    print(f'  Total parameters: {model.count_params():,}')
    
    # 训练配置
    epochs = 120
    batch_size = 16
    
    print(f'Training configuration:')
    print(f'  Epochs: {epochs}')
    print(f'  Batch size: {batch_size}')
    print(f'  Sequences: {nb_sequences}')
    print(f'  Training strategy: Alternating (Denoise → Voice Enhancement)')
    
    # 训练循环 - 交替训练
    best_denoise_loss = float('inf')
    best_voice_loss = float('inf')
    
    for epoch in range(1, epochs + 1):
        print(f"\n{'='*60}")
        print(f"Epoch {epoch}/{epochs}")
        print(f"{'='*60}")
        
        if epoch % 2 == 1:  # 奇数轮：训练降噪
            print("🔧 Training Phase: DENOISE ENHANCEMENT")
            print("-" * 40)
            
            # 编译模型用于降噪训练
            model = compile_model_for_denoise_training(model)
            
            # 训练降噪
            history = model.fit(
                X, [Y_denoise, Y_voice, Y_vad],
                batch_size=batch_size,
                epochs=1,
                verbose=1,
                validation_split=0.1
            )
            
            # 保存降噪检查点
            denoise_loss = history.history['loss'][0]
            if denoise_loss < best_denoise_loss:
                best_denoise_loss = denoise_loss
                model.save(f'models_voice_enhanced/checkpoints_denoise/denoise-{epoch:03d}-{denoise_loss:.5f}.keras')
                print(f"✓ Best denoise model saved: {denoise_loss:.5f}")
            else:
                model.save(f'models_voice_enhanced/checkpoints_denoise/denoise-{epoch:03d}-{denoise_loss:.5f}.keras')
            
        else:  # 偶数轮：训练人声增强
            print("🎤 Training Phase: VOICE ENHANCEMENT (PRIORITY)")
            print("-" * 40)
            
            # 编译模型用于人声增强训练
            model = compile_model_for_voice_training(model)
            
            # 训练人声增强
            history = model.fit(
                X, [Y_denoise, Y_voice, Y_vad],
                batch_size=batch_size,
                epochs=1,
                verbose=1,
                validation_split=0.1
            )
            
            # 保存人声增强检查点
            voice_loss = history.history['loss'][0]
            if voice_loss < best_voice_loss:
                best_voice_loss = voice_loss
                model.save(f'models_voice_enhanced/checkpoints_voice_enhance/voice-{epoch:03d}-{voice_loss:.5f}.keras')
                model.save('models_voice_enhanced/best_models/best_voice_enhanced_model.keras')
                print(f"✓ Best voice enhancement model saved: {voice_loss:.5f}")
            else:
                model.save(f'models_voice_enhanced/checkpoints_voice_enhance/voice-{epoch:03d}-{voice_loss:.5f}.keras')
        
        # 每10轮保存一次完整模型
        if epoch % 10 == 0:
            model.save(f'models_voice_enhanced/checkpoints_voice_enhance/complete-{epoch:03d}.keras')
            print(f"✓ Complete model checkpoint saved at epoch {epoch}")
    
    # 保存最终模型
    print(f"\n{'='*60}")
    print("🎉 Training completed!")
    print(f"{'='*60}")
    
    model.save('models_voice_enhanced/final_weights/separated_voice_enhanced_final.keras')
    
    # 记录训练日志
    log_content = f"""
Separated Voice Enhancement Training Log
========================================
Training completed: {datetime.datetime.now()}
Total epochs: {epochs}
Best denoise loss: {best_denoise_loss:.5f}
Best voice enhancement loss: {best_voice_loss:.5f}
Training strategy: Alternating (Denoise → Voice Enhancement)
Voice enhancement priority: HIGH
Model architecture: Separated branches with advanced voice enhancement
"""
    
    with open('models_voice_enhanced/training_logs/separated_training_log.txt', 'w', encoding='utf-8') as f:
        f.write(log_content)
    
    print("✓ Training log saved.")
    print("✓ All models saved successfully.")
    print("\nModel files:")
    print("  - Best model: models_voice_enhanced/best_models/best_voice_enhanced_model.keras")
    print("  - Final model: models_voice_enhanced/final_weights/separated_voice_enhanced_final.keras")
    print("  - Denoise checkpoints: models_voice_enhanced/checkpoints_denoise/")
    print("  - Voice enhancement checkpoints: models_voice_enhanced/checkpoints_voice_enhance/")

if __name__ == '__main__':
    main()
