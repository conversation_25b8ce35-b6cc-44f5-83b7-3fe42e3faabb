# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tsl/profiler/protobuf/trace_events.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(tsl/profiler/protobuf/trace_events.proto\x12\x0ctsl.profiler\"\xb0\x01\n\x05Trace\x12\x31\n\x07\x64\x65vices\x18\x01 \x03(\x0b\x32 .tsl.profiler.Trace.DevicesEntry\x12.\n\x0ctrace_events\x18\x04 \x03(\x0b\x32\x18.tsl.profiler.TraceEvent\x1a\x44\n\x0c\x44\x65vicesEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12#\n\x05value\x18\x02 \x01(\x0b\x32\x14.tsl.profiler.Device:\x02\x38\x01\"\xab\x01\n\x06\x44\x65vice\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tdevice_id\x18\x02 \x01(\r\x12\x36\n\tresources\x18\x03 \x03(\x0b\x32#.tsl.profiler.Device.ResourcesEntry\x1aH\n\x0eResourcesEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.tsl.profiler.Resource:\x02\x38\x01\"A\n\x08Resource\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0bresource_id\x18\x02 \x01(\r\x12\x12\n\nsort_index\x18\x03 \x01(\r\"\xcc\x01\n\nTraceEvent\x12\x11\n\tdevice_id\x18\x01 \x01(\r\x12\x13\n\x0bresource_id\x18\x02 \x01(\r\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x14\n\x0ctimestamp_ps\x18\t \x01(\x04\x12\x13\n\x0b\x64uration_ps\x18\n \x01(\x04\x12\x30\n\x04\x61rgs\x18\x0b \x03(\x0b\x32\".tsl.profiler.TraceEvent.ArgsEntry\x1a+\n\tArgsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42|\n\x18org.tensorflow.frameworkB\x11TraceEventsProtosP\x01ZHgithub.com/tensorflow/tensorflow/tensorflow/go/core/core_protos_go_proto\xf8\x01\x01\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'tsl.profiler.protobuf.trace_events_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\021TraceEventsProtosP\001ZHgithub.com/tensorflow/tensorflow/tensorflow/go/core/core_protos_go_proto\370\001\001'
  _TRACE_DEVICESENTRY._options = None
  _TRACE_DEVICESENTRY._serialized_options = b'8\001'
  _DEVICE_RESOURCESENTRY._options = None
  _DEVICE_RESOURCESENTRY._serialized_options = b'8\001'
  _TRACEEVENT_ARGSENTRY._options = None
  _TRACEEVENT_ARGSENTRY._serialized_options = b'8\001'
  _TRACE._serialized_start=59
  _TRACE._serialized_end=235
  _TRACE_DEVICESENTRY._serialized_start=167
  _TRACE_DEVICESENTRY._serialized_end=235
  _DEVICE._serialized_start=238
  _DEVICE._serialized_end=409
  _DEVICE_RESOURCESENTRY._serialized_start=337
  _DEVICE_RESOURCESENTRY._serialized_end=409
  _RESOURCE._serialized_start=411
  _RESOURCE._serialized_end=476
  _TRACEEVENT._serialized_start=479
  _TRACEEVENT._serialized_end=683
  _TRACEEVENT_ARGSENTRY._serialized_start=640
  _TRACEEVENT_ARGSENTRY._serialized_end=683
# @@protoc_insertion_point(module_scope)
