@echo off
chcp 65001 >nul
echo ================================================================
echo 🎯 Simple Voice Enhancement Tool (Working Version)
echo ================================================================
echo.

REM 检查Python环境
if not exist "..\..\training\env\python\python.exe" (
    echo ❌ Python环境不存在，请确保训练环境已正确设置
    pause
    exit /b 1
)

REM 设置Python路径
set PYTHON_PATH=..\..\training\env\python\python.exe

REM 检查是否提供了输入文件参数
if "%~1"=="" (
    echo 使用方法:
    echo   simple_test.bat input.wav [output.wav]
    echo.
    echo 参数说明:
    echo   input.wav    - 输入的WAV音频文件
    echo   output.wav   - 输出文件名 ^(可选，默认为 input_simple_enhanced.wav^)
    echo.
    echo 示例:
    echo   simple_test.bat noisy_audio.wav
    echo   simple_test.bat noisy_audio.wav clean_audio.wav
    echo.
    echo 💡 这个版本使用简化的算法，不依赖训练好的深度学习模型
    echo    包含噪声抑制和人声增强功能
    echo.
    pause
    exit /b 1
)

REM 检查输入文件是否存在
if not exist "%~1" (
    echo ❌ 输入文件不存在: %~1
    pause
    exit /b 1
)

REM 设置参数
set INPUT_FILE=%~1
set OUTPUT_FILE=%~2

echo 配置信息:
echo   输入文件: %INPUT_FILE%
if "%OUTPUT_FILE%"=="" (
    echo   输出文件: 自动生成 ^(添加_simple_enhanced后缀^)
) else (
    echo   输出文件: %OUTPUT_FILE%
)
echo   处理方法: 简化算法 ^(谱减法 + 人声增强^)
echo.

echo 开始处理音频...
echo ----------------------------------------------------------------

REM 运行简化测试脚本
if "%OUTPUT_FILE%"=="" (
    %PYTHON_PATH% simple_test.py "%INPUT_FILE%"
) else (
    %PYTHON_PATH% simple_test.py "%INPUT_FILE%" -o "%OUTPUT_FILE%"
)

if errorlevel 1 (
    echo.
    echo ❌ 音频处理失败
    pause
    exit /b 1
) else (
    echo.
    echo ================================================================
    echo 🎉 音频处理完成！
    echo ================================================================
    if "%OUTPUT_FILE%"=="" (
        for %%F in ("%INPUT_FILE%") do (
            echo   输出文件: %%~dpnF_simple_enhanced%%~xF
        )
    ) else (
        echo   输出文件: %OUTPUT_FILE%
    )
    echo   输入文件: %INPUT_FILE%
    echo.
    echo 处理效果:
    echo   ✓ 噪声抑制 ^(谱减法^)
    echo   ✓ 人声增强 ^(谐波分离^)
    echo   ✓ 动态范围压缩
    echo.
    echo 您可以播放输出文件来听取处理效果。
    echo ================================================================
)

pause
