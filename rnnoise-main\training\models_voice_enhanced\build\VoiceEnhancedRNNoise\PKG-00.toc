('D:\\RNN\\rnnoise-main\\training\\models_voice_enhanced\\build\\VoiceEnhancedRNNoise\\VoiceEnhancedRNNoise.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\RNN\\rnnoise-main\\training\\models_voice_enhanced\\build\\VoiceEnhancedRNNoise\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\RNN\\rnnoise-main\\training\\models_voice_enhanced\\build\\VoiceEnhancedRNNoise\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\RNN\\rnnoise-main\\training\\models_voice_enhanced\\build\\VoiceEnhancedRNNoise\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\RNN\\rnnoise-main\\training\\models_voice_enhanced\\build\\VoiceEnhancedRNNoise\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\RNN\\rnnoise-main\\training\\models_voice_enhanced\\build\\VoiceEnhancedRNNoise\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\RNN\\rnnoise-main\\training\\models_voice_enhanced\\build\\VoiceEnhancedRNNoise\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_tensorflow',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_tensorflow.py',
   'PYSOURCE'),
  ('voice_enhanced_test',
   'D:\\RNN\\rnnoise-main\\training\\models_voice_enhanced\\voice_enhanced_test.py',
   'PYSOURCE'),
  ('sklearn\\.libs\\vcomp140.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\.libs\\vcomp140.dll',
   'BINARY'),
  ('sklearn\\.libs\\msvcp140.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\.libs\\msvcp140.dll',
   'BINARY'),
  ('tensorflow\\lite\\experimental\\microfrontend\\python\\ops\\_audio_microfrontend_op.so',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\lite\\experimental\\microfrontend\\python\\ops\\_audio_microfrontend_op.so',
   'BINARY'),
  ('tensorflow\\compiler\\tf2xla\\ops\\_xla_ops.so',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\tf2xla\\ops\\_xla_ops.so',
   'BINARY'),
  ('tensorflow\\python\\framework\\_native_proto_caster.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_native_proto_caster.dll',
   'BINARY'),
  ('python311.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\python311.dll',
   'BINARY'),
  ('scipy.libs\\libscipy_openblas-6b2103f2ae4d8547998b5d188e9801fb.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy.libs\\libscipy_openblas-6b2103f2ae4d8547998b5d188e9801fb.dll',
   'BINARY'),
  ('_soundfile_data\\libsndfile_x64.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\_soundfile_data\\libsndfile_x64.dll',
   'BINARY'),
  ('llvmlite\\binding\\llvmlite.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\llvmlite\\binding\\llvmlite.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-c16e4918366c6bc1f1cd71e28ca36fc0.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-c16e4918366c6bc1f1cd71e28ca36fc0.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-d64049c6e3865410a7dda6a7e9f0c575.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy.libs\\msvcp140-d64049c6e3865410a7dda6a7e9f0c575.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_socket.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_ctypes.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_overlapped.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_queue.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_asyncio.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_kernel_registry.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_kernel_registry.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_tfprof.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_tfprof.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\framework\\_op_def_library_pybind.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_op_def_library_pybind.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\_pywrap_tensorflow_internal.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_tensorflow_internal.pyd',
   'EXTENSION'),
  ('google\\_upb\\_message.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\google\\_upb\\_message.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\flags_pybind.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\flags_pybind.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\framework\\_op_def_registry.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_op_def_registry.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\client\\_pywrap_tf_session.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\client\\_pywrap_tf_session.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\client\\_pywrap_debug_events_writer.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\client\\_pywrap_debug_events_writer.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\client\\_pywrap_events_writer.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\client\\_pywrap_events_writer.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\client\\_pywrap_device_lib.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\client\\_pywrap_device_lib.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\profiler\\internal\\_pywrap_traceme.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\profiler\\internal\\_pywrap_traceme.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\profiler\\internal\\_pywrap_profiler_plugin.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\profiler\\internal\\_pywrap_profiler_plugin.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\framework\\_proto_comparators.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_proto_comparators.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\profiler\\internal\\_pywrap_profiler.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\profiler\\internal\\_pywrap_profiler.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\saved_model\\pywrap_saved_model.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\saved_model\\pywrap_saved_model.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_checkpoint_reader.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_checkpoint_reader.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\lib\\io\\_pywrap_record_io.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\lib\\io\\_pywrap_record_io.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\lib\\io\\_pywrap_file_io.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\lib\\io\\_pywrap_file_io.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\_uuid.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\tpu\\_pywrap_sparse_core_layout.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\tpu\\_pywrap_sparse_core_layout.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\lib\\core\\_pywrap_py_func.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\lib\\core\\_pywrap_py_func.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\data\\experimental\\service\\_pywrap_snapshot_utils.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\data\\experimental\\service\\_pywrap_snapshot_utils.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\data\\experimental\\service\\_pywrap_utils_exp.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\data\\experimental\\service\\_pywrap_utils_exp.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\data\\experimental\\service\\_pywrap_server_lib.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\data\\experimental\\service\\_pywrap_server_lib.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\_pywrap_dtensor_device.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_dtensor_device.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\_pywrap_parallel_device.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_parallel_device.pyd',
   'EXTENSION'),
  ('h5py\\utils.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5ac.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5ac.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\defs.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\defs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_proxy.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\_proxy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_objects.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\_objects.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_npystrings.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\_npystrings.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5l.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5l.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5o.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5o.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_selector.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\_selector.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5i.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5i.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5pl.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5pl.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5p.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5p.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5t.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5t.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5s.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5s.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5r.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5r.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5g.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5g.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5fd.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5fd.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5f.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5f.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5ds.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5ds.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5d.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5d.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5a.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5a.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5z.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5z.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_conv.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\_conv.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\h5.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\h5.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('h5py\\_errors.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\_errors.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('ml_dtypes\\_ml_dtypes_ext.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\ml_dtypes\\_ml_dtypes_ext.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\_pywrap_py_exception_registry.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_py_exception_registry.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\_pywrap_tfe.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_tfe.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\framework\\_dtypes.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_dtypes.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\framework\\_test_metrics_util.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_test_metrics_util.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\_pywrap_sanitizers.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_sanitizers.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\framework\\_pywrap_python_op_gen.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_pywrap_python_op_gen.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\grappler\\_pywrap_tf_cluster.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\grappler\\_pywrap_tf_cluster.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\grappler\\_pywrap_tf_optimizer.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\grappler\\_pywrap_tf_optimizer.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\framework\\_pywrap_python_api_dispatcher.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_pywrap_python_api_dispatcher.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\platform\\_pywrap_stacktrace_handler.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\platform\\_pywrap_stacktrace_handler.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\platform\\_pywrap_tf2.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\platform\\_pywrap_tf2.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\platform\\_pywrap_cpu_feature_guard.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\platform\\_pywrap_cpu_feature_guard.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\fast_module_type.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\fast_module_type.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_util_port.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_util_port.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_tensor_float_32_execution.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_tensor_float_32_execution.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_determinism.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_determinism.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_utils.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_utils.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_nest.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_nest.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_tf_stack.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_tf_stack.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\_pywrap_mlir.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_mlir.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_ccallback_c.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\_lib\\_ccallback_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_fblas.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_fblas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_flapack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_flapack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_lapack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\cython_lapack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\cython_blas.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\cython_blas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_update.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_decomp_update.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ellip_harm_2.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\special\\_ellip_harm_2.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_core.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_core.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_highspy\\_highs_options.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_highspy\\_highs_options.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_direct.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_direct.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmc_cy.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\stats\\_qmc_cy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_sobol.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\stats\\_sobol.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_wrap.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\spatial\\_distance_wrap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_distance_pybind.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\spatial\\_distance_pybind.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_hausdorff.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\spatial\\_hausdorff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_tools.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_tools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_reordering.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_reordering.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_matching.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_matching.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_flow.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_flow.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_min_spanning_tree.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_min_spanning_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_traversal.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_traversal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\csgraph\\_shortest_path.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\csgraph\\_shortest_path.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_rank_filter_1d.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\ndimage\\_rank_filter_1d.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_nd_image.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\ndimage\\_nd_image.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\ndimage\\_ni_label.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\ndimage\\_ni_label.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rgi_cython.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\interpolate\\_rgi_cython.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_interpnd.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\interpolate\\_interpnd.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_rbfinterp_pythran.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\interpolate\\_rbfinterp_pythran.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_ppoly.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\interpolate\\_ppoly.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dierckx.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\interpolate\\_dierckx.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_dfitpack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\interpolate\\_dfitpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\interpolate\\_fitpack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\interpolate\\_fitpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats_pythran.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\stats\\_stats_pythran.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\fft\\_pocketfft\\pypocketfft.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\pypocketfft.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_uarray\\_uarray.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\_lib\\_uarray\\_uarray.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_rcont\\rcont.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\stats\\_rcont\\rcont.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_qmvnt_cy.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\stats\\_qmvnt_cy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_ansari_swilk_statistics.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\stats\\_ansari_swilk_statistics.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_levy_stable\\levyst.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\stats\\_levy_stable\\levyst.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_biasedurn.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\stats\\_biasedurn.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\stats\\_stats.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\stats\\_stats.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\cython_special.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\special\\cython_special.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rotation.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\spatial\\transform\\_rotation.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\transform\\_rigid_transform.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\spatial\\transform\\_rigid_transform.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_voronoi.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\spatial\\_voronoi.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_qhull.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\spatial\\_qhull.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\spatial\\_ckdtree.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\spatial\\_ckdtree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_cyutility.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\_cyutility.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_pava_pybind.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_pava_pybind.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsq\\givens_elimination.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_lsq\\givens_elimination.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lsap.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_lsap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_interpolative.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_decomp_interpolative.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_bglu_dense.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_bglu_dense.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_slsqplib.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_slsqplib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_zeros.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_zeros.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_minpack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_minpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_trlib\\_trlib.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_trlib\\_trlib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_moduleTNC.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_moduleTNC.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_lbfgsb.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_lbfgsb.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\optimize\\_group_columns.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\optimize\\_group_columns.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_lsoda.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\integrate\\_lsoda.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_dop.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\integrate\\_dop.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_vode.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\integrate\\_vode.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_quadpack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\integrate\\_quadpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\integrate\\_odepack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\integrate\\_odepack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_gufuncs.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\special\\_gufuncs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_special_ufuncs.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\special\\_special_ufuncs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_comb.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\special\\_comb.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_specfun.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\special\\_specfun.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\special\\_ufuncs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\special\\_ufuncs_cxx.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\special\\_ufuncs_cxx.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_sqrtm_triu.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_sqrtm_triu.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_linalg_pythran.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_linalg_pythran.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_expm.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_expm.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_matfuncs_schur_sqrtm.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_matfuncs_schur_sqrtm.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_decomp_lu_cython.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_decomp_lu_cython.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_solve_toeplitz.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_solve_toeplitz.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\linalg\\_cythonized_array_utils.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\linalg\\_cythonized_array_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_zpropack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_zpropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_cpropack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_cpropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_dpropack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_dpropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_propack\\_spropack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_propack\\_spropack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\_arpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\linalg\\_dsolve\\_superlu.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\_superlu.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_csparsetools.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\_csparsetools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\sparse\\_sparsetools.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\sparse\\_sparsetools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\_fpumode.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\_lib\\_fpumode.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\_lib\\messagestream.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\_lib\\messagestream.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('tensorflow\\compiler\\tf2tensorrt\\_pywrap_py_utils.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\tf2tensorrt\\_pywrap_py_utils.pyd',
   'EXTENSION'),
  ('wrapt\\_wrappers.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\wrapt\\_wrappers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('optree\\_C.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\optree\\_C.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_isfinite.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_isfinite.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\sparsefuncs_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_cyutility.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_cyutility.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_cython_blas.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\murmurhash.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\murmurhash.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_typedefs.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_typedefs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_vector_sentinel.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_sorting.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_sorting.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_heap.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_heap.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_pairwise_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_random.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_random.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\preprocessing\\_target_encoder_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_partition_nodes.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_criterion.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_criterion.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_partitioner.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_partitioner.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_utils.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_tree.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\tree\\_splitter.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_splitter.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_quad_tree.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_weight_vector.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_sgd_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_loss\\_loss.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_loss\\_loss.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_sag_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_openmp_helpers.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_libsvm_sparse.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_libsvm.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_libsvm.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\svm\\_liblinear.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_liblinear.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\arrayfuncs.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\linear_model\\_cd_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_seq_dataset.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\decomposition\\_cdnmf_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\decomposition\\_online_lda_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_kd_tree.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\neighbors\\_ball_tree.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\manifold\\_utils.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\manifold\\_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\manifold\\_barnes_hut_tsne.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\_isotonic.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_isotonic.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_minibatch.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_lloyd.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_elkan.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_k_means_common.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_tree.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_dbscan_inner.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_optimal_leaf_ordering.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\cluster\\_optimal_leaf_ordering.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_hierarchy.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\cluster\\_hierarchy.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\cluster\\_vq.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\cluster\\_vq.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\utils\\_fast_dict.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\cluster\\_hierarchical_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\_dist_metrics.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\__check_build\\_check_build.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\pywrap_xla_ops.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\pywrap_xla_ops.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_transform_graph.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_transform_graph.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\util\\_pywrap_stat_summarizer.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_stat_summarizer.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\tpu\\_pywrap_tpu_embedding.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\tpu\\_pywrap_tpu_embedding.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\grappler\\_pywrap_tf_item.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\grappler\\_pywrap_tf_item.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\framework\\_python_memory_checker_helper.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_python_memory_checker_helper.pyd',
   'EXTENSION'),
  ('grpc\\_cython\\cygrpc.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\grpc\\_cython\\cygrpc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\_pywrap_tfcompile.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_tfcompile.pyd',
   'EXTENSION'),
  ('tensorflow\\python\\_pywrap_quantize_training.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_quantize_training.pyd',
   'EXTENSION'),
  ('tensorflow\\lite\\python\\optimize\\_pywrap_tensorflow_lite_calibration_wrapper.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\lite\\python\\optimize\\_pywrap_tensorflow_lite_calibration_wrapper.pyd',
   'EXTENSION'),
  ('tensorflow\\lite\\python\\metrics\\_pywrap_tensorflow_lite_metrics_wrapper.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\lite\\python\\metrics\\_pywrap_tensorflow_lite_metrics_wrapper.pyd',
   'EXTENSION'),
  ('tensorflow\\lite\\python\\interpreter_wrapper\\_pywrap_tensorflow_interpreter_wrapper.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\lite\\python\\interpreter_wrapper\\_pywrap_tensorflow_interpreter_wrapper.pyd',
   'EXTENSION'),
  ('tensorflow\\lite\\python\\analyzer_wrapper\\_pywrap_analyzer_wrapper.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\lite\\python\\analyzer_wrapper\\_pywrap_analyzer_wrapper.pyd',
   'EXTENSION'),
  ('tensorflow\\compiler\\mlir\\stablehlo\\stablehlo_extension.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\mlir\\stablehlo\\stablehlo_extension.pyd',
   'EXTENSION'),
  ('tensorflow\\compiler\\mlir\\quantization\\tensorflow\\python\\pywrap_quantize_model.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\mlir\\quantization\\tensorflow\\python\\pywrap_quantize_model.pyd',
   'EXTENSION'),
  ('tensorflow\\compiler\\mlir\\quantization\\tensorflow\\python\\pywrap_function_lib.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\mlir\\quantization\\tensorflow\\python\\pywrap_function_lib.pyd',
   'EXTENSION'),
  ('tensorflow\\compiler\\mlir\\lite\\python\\_pywrap_converter_api.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\mlir\\lite\\python\\_pywrap_converter_api.pyd',
   'EXTENSION'),
  ('numba\\core\\typeconv\\_typeconv.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\core\\typeconv\\_typeconv.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\experimental\\jitclass\\_box.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\experimental\\jitclass\\_box.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\core\\runtime\\_nrt_python.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\core\\runtime\\_nrt_python.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\workqueue.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\np\\ufunc\\workqueue.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\omppool.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\np\\ufunc\\omppool.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\tbbpool.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\np\\ufunc\\tbbpool.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\np\\ufunc\\_internal.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\np\\ufunc\\_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\cuda\\cudadrv\\_extras.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\cuda\\cudadrv\\_extras.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_devicearray.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\_devicearray.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\mviewbuf.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\mviewbuf.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_dispatcher.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\_dispatcher.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_helperlib.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\_helperlib.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numba\\_dynfunc.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numba\\_dynfunc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('msgpack\\_cmsgpack.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\msgpack\\_cmsgpack.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_peak_finding_utils.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\signal\\_peak_finding_utils.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_sosfilt.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\signal\\_sosfilt.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_spline.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\signal\\_spline.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_upfirdn_apply.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\signal\\_upfirdn_apply.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_max_len_seq_inner.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\signal\\_max_len_seq_inner.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('scipy\\signal\\_sigtools.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\scipy\\signal\\_sigtools.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('sklearn\\feature_extraction\\_hashing_fast.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\feature_extraction\\_hashing_fast.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('soxr\\soxr_ext.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\soxr\\soxr_ext.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140_1.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\VCRUNTIME140.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\Windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('libcrypto-3.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\libffi-8.dll',
   'BINARY'),
  ('python3.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\python3.dll',
   'BINARY'),
  ('h5py\\hdf5.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\hdf5.dll',
   'BINARY'),
  ('h5py\\hdf5_hl.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\hdf5_hl.dll',
   'BINARY'),
  ('VCOMP140.DLL', 'C:\\Windows\\system32\\VCOMP140.DLL', 'BINARY'),
  ('h5py\\zlib.dll',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\h5py\\zlib.dll',
   'BINARY'),
  ('certifi\\cacert.pem',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.pyx',
   'DATA'),
  ('sklearn\\preprocessing\\_target_encoder_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdf-42074.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdf-42074.json.gz',
   'DATA'),
  ('sklearn\\utils\\sparsefuncs_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.pyx',
   'DATA'),
  ('sklearn\\_loss\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_loss\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-s-act-.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-dv-1.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\histogram.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdf-40675.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdf-40675.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.pyx',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\svm.cpp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\svm.cpp',
   'DATA'),
  ('sklearn\\utils\\_heap.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_heap.pyx',
   'DATA'),
  ('sklearn\\utils\\_random.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_random.pyx',
   'DATA'),
  ('sklearn\\_isotonic.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_isotonic.pyx',
   'DATA'),
  ('sklearn\\decomposition\\_online_lda_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jd-561.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jd-561.json.gz',
   'DATA'),
  ('sklearn\\manifold\\_utils.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\manifold\\_utils.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\data-v1-dl-61.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\data-v1-dl-61.arff.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pyx.tp',
   'DATA'),
  ('sklearn\\tree\\_utils.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_utils.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdq-1.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdq-1.json.gz',
   'DATA'),
  ('sklearn\\utils\\_typedefs.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_typedefs.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\images\\flower.jpg',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\images\\flower.jpg',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jd-2.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jd-2.json.gz',
   'DATA'),
  ('sklearn\\feature_extraction\\_hashing_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\feature_extraction\\_hashing_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_invalid.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_invalid.txt',
   'DATA'),
  ('sklearn\\tree\\_tree.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_tree.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\decomposition\\_cdnmf_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdq-40589.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdq-40589.json.gz',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\estimator.js',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\estimator.js',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\_svm_cython_blas_helpers.h',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\_svm_cython_blas_helpers.h',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1-s-dact.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1-s-dact.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-dv-1.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_gradient_boosting.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_gradient_boosting.pyx',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pyx.tp',
   'DATA'),
  ('sklearn\\cluster\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\data\\linnerud_exercise.csv',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\data\\linnerud_exercise.csv',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1-s-dact.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1-s-dact.json.gz',
   'DATA'),
  ('sklearn\\svm\\_liblinear.pxi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_liblinear.pxi',
   'DATA'),
  ('sklearn\\datasets\\descr\\diabetes.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\diabetes.rst',
   'DATA'),
  ('sklearn\\svm\\_libsvm.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_libsvm.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\data\\breast_cancer.csv',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\data\\breast_cancer.csv',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jd-40589.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jd-40589.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\data-v1-dl-16826755.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\data-v1-dl-16826755.arff.gz',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdf-42585.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdf-42585.json.gz',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.pxd.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.pxd.tp',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\splitting.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\splitting.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdq-561.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdq-561.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\data-v1-dl-54002.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\data-v1-dl-54002.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\data-v1-dl-4644182.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\data-v1-dl-4644182.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\LICENSE',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\LICENSE',
   'DATA'),
  ('sklearn\\neighbors\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\meson.build',
   'DATA'),
  ('sklearn\\svm\\_libsvm_sparse.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-s-act-.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdl-dn-australian-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\data-v1-dl-21854866.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\data-v1-dl-21854866.arff.gz',
   'DATA'),
  ('sklearn\\svm\\_libsvm_sparse.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_libsvm_sparse.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-292.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-292.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\manifold\\_utils.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\manifold\\_utils.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_typedefs.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_typedefs.pxd',
   'DATA'),
  ('sklearn\\ensemble\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\meson.build',
   'DATA'),
  ('sklearn\\feature_extraction\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\feature_extraction\\meson.build',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdf-1590.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdf-1590.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.pyx.tp',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\_compat.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\_compat.pyi',
   'DATA'),
  ('sklearn\\datasets\\images\\README.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\images\\README.txt',
   'DATA'),
  ('sklearn\\tree\\_partitioner.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_partitioner.pyx',
   'DATA'),
  ('sklearn\\svm\\_liblinear.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_liblinear.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_k_means_lloyd.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_random.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_random.pxd',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\estimator.css',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\estimator.css',
   'DATA'),
  ('sklearn\\svm\\_liblinear.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_liblinear.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-s-act-.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-dv-4.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-dv-4.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-dv-3.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdl-dn-emotions-l-2-dv-3.json.gz',
   'DATA'),
  ('sklearn\\utils\\_isfinite.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_isfinite.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-s-act-.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-40981.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jd-40981.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_k_means_elkan.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\tron.cpp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\tron.cpp',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\linear.h',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\linear.h',
   'DATA'),
  ('sklearn\\datasets\\data\\diabetes_target.csv.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\data\\diabetes_target.csv.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdq-40945.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdq-40945.json.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\iris.csv',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\data\\iris.csv',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jd-1119.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jd-1119.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_k_means_minibatch.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jd-40945.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jd-40945.json.gz',
   'DATA'),
  ('sklearn\\datasets\\data\\diabetes_data_raw.csv.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\data\\diabetes_data_raw.csv.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_gradient_boosting.pyx',
   'DATA'),
  ('sklearn\\datasets\\descr\\lfw.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\lfw.rst',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdq-1590.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jdq-1590.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\data-v1-dl-1.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\data-v1-dl-1.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdf-1.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jdf-1.json.gz',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.pyx',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\README.md',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\README.md',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\data-v1-dl-52739.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\data-v1-dl-52739.arff.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\meson.build',
   'DATA'),
  ('sklearn\\utils\\murmurhash.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\murmurhash.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pyx.tp',
   'DATA'),
  ('sklearn\\cluster\\_k_means_lloyd.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_lloyd.pyx',
   'DATA'),
  ('sklearn\\utils\\src\\MurmurHash3.cpp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\src\\MurmurHash3.cpp',
   'DATA'),
  ('sklearn\\svm\\_libsvm.pxi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_libsvm.pxi',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\data-v1-dl-3.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\data-v1-dl-3.arff.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jd-1.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1\\api-v1-jd-1.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pxd.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pxd.tp',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.pyx',
   'DATA'),
  ('sklearn\\_loss\\_loss.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_loss\\_loss.pyx.tp',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\COPYRIGHT',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\COPYRIGHT',
   'DATA'),
  ('sklearn\\tree\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\meson.build',
   'DATA'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\descr\\twenty_newsgroups.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\twenty_newsgroups.rst',
   'DATA'),
  ('sklearn\\utils\\_seq_dataset.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_seq_dataset.pyx.tp',
   'DATA'),
  ('sklearn\\utils\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\meson.build',
   'DATA'),
  ('sklearn\\svm\\_newrand.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_newrand.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdf-40945.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40945\\api-v1-jdf-40945.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdq-1119.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdq-1119.json.gz',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.pyx',
   'DATA'),
  ('sklearn\\svm\\_newrand.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_newrand.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdf-2.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdf-2.json.gz',
   'DATA'),
  ('sklearn\\utils\\arrayfuncs.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pxd.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pxd.tp',
   'DATA'),
  ('sklearn\\tree\\_splitter.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_splitter.pyx',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdq-42074.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jdq-42074.json.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\breast_cancer.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\breast_cancer.rst',
   'DATA'),
  ('sklearn\\cluster\\_dbscan_inner.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\descr\\rcv1.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\rcv1.rst',
   'DATA'),
  ('sklearn\\utils\\_fast_dict.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_fast_dict.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jd-61.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jd-61.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\histogram.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\histogram.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdf-40589.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40589\\api-v1-jdf-40589.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdq-3.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdq-3.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_classification.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_classification.txt',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdf-40966.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdf-40966.json.gz',
   'DATA'),
  ('sklearn\\tree\\_tree.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_tree.pxd',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\data-v1-dl-1666876.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\data-v1-dl-1666876.arff.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.pyx.tp',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\preprocessing\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\preprocessing\\meson.build',
   'DATA'),
  ('sklearn\\neighbors\\_ball_tree.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\descr\\wine_data.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\wine_data.rst',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\libsvm_sparse_helper.c',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\libsvm_sparse_helper.c',
   'DATA'),
  ('sklearn\\metrics\\cluster\\_expected_mutual_info_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\cluster\\_expected_mutual_info_fast.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.pyx',
   'DATA'),
  ('sklearn\\manifold\\_barnes_hut_tsne.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-292.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-292.json.gz',
   'DATA'),
  ('sklearn\\manifold\\_barnes_hut_tsne.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\manifold\\_barnes_hut_tsne.pyx',
   'DATA'),
  ('sklearn\\tree\\_utils.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_utils.pxd',
   'DATA'),
  ('sklearn\\linear_model\\_cd_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.pxd',
   'DATA'),
  ('sklearn\\externals\\array_api_compat\\py.typed',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\externals\\array_api_compat\\py.typed',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jd-62.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jd-62.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-s-act-.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdl-dn-iris-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_gradient_boosting.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_gradient_boosting.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_linkage.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_linkage.pyx',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jd-3.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jd-3.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_predictor.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-dv-1.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdl-dn-cpu-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\neighbors\\_quad_tree.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_quad_tree.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\data-v1-dl-4965250.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\data-v1-dl-4965250.arff.gz',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.pxd',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin_classmode.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_heap.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_heap.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\data-v1-dl-49822.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\data-v1-dl-49822.arff.gz',
   'DATA'),
  ('sklearn\\neighbors\\_kd_tree.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.pyx.tp',
   'DATA'),
  ('sklearn\\utils\\_isfinite.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_isfinite.pyx',
   'DATA'),
  ('sklearn\\svm\\src\\newrand\\newrand.h',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\newrand\\newrand.h',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jd-40675.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jd-40675.json.gz',
   'DATA'),
  ('sklearn\\datasets\\descr\\california_housing.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\california_housing.rst',
   'DATA'),
  ('sklearn\\utils\\_sorting.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_sorting.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-dv-1.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\datasets\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\meson.build',
   'DATA'),
  ('sklearn\\linear_model\\_sag_fast.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdf-1119.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdf-1119.json.gz',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\svm.h',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\svm.h',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\data-v1-dl-52352.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\data-v1-dl-52352.arff.gz',
   'DATA'),
  ('sklearn\\tree\\_partitioner.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_partitioner.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\_libsvm.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\_libsvm.pyx',
   'DATA'),
  ('sklearn\\utils\\_random.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_random.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\neighbors\\_ball_tree.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_ball_tree.pyx.tp',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_base.pxd.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_base.pxd.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jd-1590.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\api-v1-jd-1590.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-40981.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_292\\api-v1-jdf-40981.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_k_means_minibatch.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_minibatch.pyx',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\_lib\\_utils\\_typing.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\_lib\\_utils\\_typing.pyi',
   'DATA'),
  ('sklearn\\datasets\\_svmlight_format_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\_svmlight_format_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\_isotonic.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_isotonic.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\splitting.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\murmurhash.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\murmurhash.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdq-40966.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdq-40966.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.pyx',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\data\\wine_data.csv',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\data\\wine_data.csv',
   'DATA'),
  ('sklearn\\utils\\_sorting.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_sorting.pxd',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\svm\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdf-561.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_561\\api-v1-jdf-561.json.gz',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\libsvm_helper.c',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\libsvm_helper.c',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\libsvm_template.cpp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\libsvm_template.cpp',
   'DATA'),
  ('sklearn\\decomposition\\_online_lda_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\decomposition\\_online_lda_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_classmode.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_classmode.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-s-act-.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1119\\api-v1-jdl-dn-adult-census-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jd-40966.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jd-40966.json.gz',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_binning.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\cluster\\_hierarchical_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hierarchical_fast.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_binning.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_binning.pyx',
   'DATA'),
  ('sklearn\\__check_build\\_check_build.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_splitter.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_splitter.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\_cd_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\_cd_fast.pyx',
   'DATA'),
  ('sklearn\\neighbors\\_kd_tree.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_kd_tree.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\meson.build',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.pxd',
   'DATA'),
  ('sklearn\\datasets\\data\\digits.csv.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\data\\digits.csv.gz',
   'DATA'),
  ('sklearn\\neighbors\\_binary_tree.pxi.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_binary_tree.pxi.tp',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\linear.cpp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\linear.cpp',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\LICENSE',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\LICENSE',
   'DATA'),
  ('sklearn\\datasets\\descr\\kddcup99.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\kddcup99.rst',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pxd.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.pxd.tp',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\py.typed',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\py.typed',
   'DATA'),
  ('sklearn\\utils\\src\\MurmurHash3.h',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\src\\MurmurHash3.h',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jd-42585.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jd-42585.json.gz',
   'DATA'),
  ('sklearn\\neighbors\\_partition_nodes.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\neighbors\\_partition_nodes.pxd',
   'DATA'),
  ('sklearn\\tree\\_criterion.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_criterion.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\cluster\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\cluster\\meson.build',
   'DATA'),
  ('sklearn\\manifold\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\manifold\\meson.build',
   'DATA'),
  ('sklearn\\_cyutility.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_cyutility.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\preprocessing\\_target_encoder_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\preprocessing\\_target_encoder_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pxd.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pxd.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_multilabel.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_multilabel.txt',
   'DATA'),
  ('sklearn\\tree\\_splitter.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_splitter.pxd',
   'DATA'),
  ('sklearn\\datasets\\images\\china.jpg',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\images\\china.jpg',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdf-61.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdf-61.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdq-40675.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdq-40675.json.gz',
   'DATA'),
  ('sklearn\\utils\\_sorting.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_sorting.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\data-v1-dl-21552912.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\data-v1-dl-21552912.arff.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_middle_term_computer.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\descr\\digits.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\digits.rst',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.pxd.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.pxd.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-s-act-.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdl-dn-anneal-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\liblinear_helper.c',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\liblinear_helper.c',
   'DATA'),
  ('sklearn\\utils\\_weight_vector.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_weight_vector.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\tree\\_criterion.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_criterion.pyx',
   'DATA'),
  ('sklearn\\linear_model\\_sag_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\_sag_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\externals\\array_api_extra\\README.md',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\externals\\array_api_extra\\README.md',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\meson.build',
   'DATA'),
  ('sklearn\\preprocessing\\_csr_polynomial_expansion.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\preprocessing\\_csr_polynomial_expansion.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\__check_build\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\__check_build\\meson.build',
   'DATA'),
  ('sklearn\\_loss\\_loss.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_loss\\_loss.pxd',
   'DATA'),
  ('sklearn\\cluster\\_k_means_elkan.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_elkan.pyx',
   'DATA'),
  ('sklearn\\utils\\murmurhash.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\murmurhash.pxd',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pxd.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_argkmin.pxd.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdq-61.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_61\\api-v1-jdq-61.json.gz',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\common.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\common.pyx',
   'DATA'),
  ('sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\_bitset.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\data-v1-dl-17928620.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\data-v1-dl-17928620.arff.gz',
   'DATA'),
  ('sklearn\\svm\\src\\libsvm\\LIBSVM_CHANGES',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\libsvm\\LIBSVM_CHANGES',
   'DATA'),
  ('sklearn\\utils\\_cython_blas.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_cython_blas.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\svmlight_invalid_order.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\svmlight_invalid_order.txt',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdq-62.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdq-62.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\externals\\README',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\externals\\README',
   'DATA'),
  ('sklearn\\tree\\_criterion.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_criterion.pxd',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\_svmlight_format_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\_svmlight_format_fast.pyx',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\descr\\linnerud.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\linnerud.rst',
   'DATA'),
  ('sklearn\\utils\\_typedefs.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_typedefs.pyx',
   'DATA'),
  ('sklearn\\utils\\_repr_html\\params.css',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_repr_html\\params.css',
   'DATA'),
  ('sklearn\\datasets\\descr\\iris.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\iris.rst',
   'DATA'),
  ('sklearn\\cluster\\_k_means_common.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_k_means_common.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdf-62.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_62\\api-v1-jdf-62.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\__check_build\\_check_build.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\__check_build\\_check_build.pyx',
   'DATA'),
  ('sklearn\\datasets\\descr\\species_distributions.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\species_distributions.rst',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_tree.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_tree.pxd',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_1590\\data-v1-dl-1595261.arff.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_1590\\data-v1-dl-1595261.arff.gz',
   'DATA'),
  ('sklearn\\cluster\\_dbscan_inner.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_dbscan_inner.pyx',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40675\\api-v1-jdl-dn-glass2-l-2-dv-1.json.gz',
   'DATA'),
  ('sklearn\\tree\\_utils.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_utils.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\utils\\arrayfuncs.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\arrayfuncs.pyx',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\_cython_blas_helpers.h',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\_cython_blas_helpers.h',
   'DATA'),
  ('sklearn\\utils\\sparsefuncs_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\sparsefuncs_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\descr\\olivetti_faces.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\olivetti_faces.rst',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_datasets_pair.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdf-3.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_3\\api-v1-jdf-3.json.gz',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdq-42585.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42585\\api-v1-jdq-42585.json.gz',
   'DATA'),
  ('sklearn\\linear_model\\_sgd_fast.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\_loss\\_loss.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\_loss\\_loss.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\meson.build',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_distances_reduction\\_radius_neighbors_classmode.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\datasets\\data\\linnerud_physiological.csv',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\data\\linnerud_physiological.csv',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-s-act-.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_40966\\api-v1-jdl-dn-miceprotein-l-2-s-act-.json.gz',
   'DATA'),
  ('sklearn\\tree\\_tree.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_tree.pyx',
   'DATA'),
  ('sklearn\\decomposition\\_cdnmf_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\decomposition\\_cdnmf_fast.pyx',
   'DATA'),
  ('sklearn\\feature_extraction\\_hashing_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\feature_extraction\\_hashing_fast.pyx',
   'DATA'),
  ('sklearn\\cluster\\_hdbscan\\_reachability.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\_reachability.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\linear_model\\_sgd_fast.pyx.tp',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\linear_model\\_sgd_fast.pyx.tp',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jd-42074.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_42074\\api-v1-jd-42074.json.gz',
   'DATA'),
  ('sklearn\\metrics\\_dist_metrics.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_dist_metrics.pxd',
   'DATA'),
  ('sklearn\\decomposition\\meson.build',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\decomposition\\meson.build',
   'DATA'),
  ('sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdq-2.json.gz',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\tests\\data\\openml\\id_2\\api-v1-jdq-2.json.gz',
   'DATA'),
  ('sklearn\\utils\\_openmp_helpers.cp311-win_amd64.lib',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_openmp_helpers.cp311-win_amd64.lib',
   'DATA'),
  ('sklearn\\metrics\\_pairwise_fast.pyx',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\metrics\\_pairwise_fast.pyx',
   'DATA'),
  ('sklearn\\utils\\_heap.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_heap.pxd',
   'DATA'),
  ('sklearn\\tree\\_partitioner.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\tree\\_partitioner.pxd',
   'DATA'),
  ('sklearn\\utils\\_vector_sentinel.pxd',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\utils\\_vector_sentinel.pxd',
   'DATA'),
  ('sklearn\\datasets\\descr\\covtype.rst',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\datasets\\descr\\covtype.rst',
   'DATA'),
  ('sklearn\\svm\\src\\liblinear\\tron.h',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\sklearn\\svm\\src\\liblinear\\tron.h',
   'DATA'),
  ('tensorflow\\python\\client\\_pywrap_device_lib.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\client\\_pywrap_device_lib.pyi',
   'DATA'),
  ('tensorflow\\python\\flags_pybind.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\flags_pybind.pyi',
   'DATA'),
  ('tensorflow\\python\\saved_model\\pywrap_saved_model\\constants.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\saved_model\\pywrap_saved_model\\constants.pyi',
   'DATA'),
  ('tensorflow\\python\\platform\\_pywrap_cpu_feature_guard.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\platform\\_pywrap_cpu_feature_guard.pyi',
   'DATA'),
  ('tensorflow\\python\\platform\\_pywrap_stacktrace_handler.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\platform\\_pywrap_stacktrace_handler.pyi',
   'DATA'),
  ('tensorflow\\python\\data\\experimental\\service\\_pywrap_server_lib.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\data\\experimental\\service\\_pywrap_server_lib.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_nest.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_nest.pyi',
   'DATA'),
  ('tensorflow\\lite\\python\\analyzer_wrapper\\_pywrap_analyzer_wrapper.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\lite\\python\\analyzer_wrapper\\_pywrap_analyzer_wrapper.pyi',
   'DATA'),
  ('tensorflow\\python\\framework\\_op_def_registry.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_op_def_registry.pyi',
   'DATA'),
  ('tensorflow\\THIRD_PARTY_NOTICES.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\THIRD_PARTY_NOTICES.txt',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_checkpoint_reader.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_checkpoint_reader.pyi',
   'DATA'),
  ('tensorflow\\python\\framework\\_op_def_library_pybind.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_op_def_library_pybind.pyi',
   'DATA'),
  ('tensorflow\\compiler\\mlir\\quantization\\tensorflow\\python\\pywrap_quantize_model.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\mlir\\quantization\\tensorflow\\python\\pywrap_quantize_model.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_util_port.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_util_port.pyi',
   'DATA'),
  ('tensorflow\\lite\\python\\interpreter_wrapper\\_pywrap_tensorflow_interpreter_wrapper.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\lite\\python\\interpreter_wrapper\\_pywrap_tensorflow_interpreter_wrapper.pyi',
   'DATA'),
  ('tensorflow\\python\\grappler\\_pywrap_tf_cluster.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\grappler\\_pywrap_tf_cluster.pyi',
   'DATA'),
  ('tensorflow\\python\\_pywrap_py_exception_registry.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_py_exception_registry.pyi',
   'DATA'),
  ('tensorflow\\python\\saved_model\\pywrap_saved_model\\merger.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\saved_model\\pywrap_saved_model\\merger.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_tf_stack.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_tf_stack.pyi',
   'DATA'),
  ('tensorflow\\python\\lib\\core\\_pywrap_py_func.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\lib\\core\\_pywrap_py_func.pyi',
   'DATA'),
  ('tensorflow\\_api\\v2\\api_packages.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\_api\\v2\\api_packages.txt',
   'DATA'),
  ('tensorflow\\python\\platform\\_pywrap_tf2.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\platform\\_pywrap_tf2.pyi',
   'DATA'),
  ('tensorflow\\python\\lib\\io\\_pywrap_file_io.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\lib\\io\\_pywrap_file_io.pyi',
   'DATA'),
  ('tensorflow\\python\\data\\experimental\\service\\_pywrap_utils_exp.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\data\\experimental\\service\\_pywrap_utils_exp.pyi',
   'DATA'),
  ('tensorflow\\python\\client\\_pywrap_debug_events_writer.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\client\\_pywrap_debug_events_writer.pyi',
   'DATA'),
  ('tensorflow\\python\\autograph\\impl\\testing\\pybind_for_testing.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\impl\\testing\\pybind_for_testing.pyi',
   'DATA'),
  ('tensorflow\\lite\\python\\optimize\\_pywrap_tensorflow_lite_calibration_wrapper.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\lite\\python\\optimize\\_pywrap_tensorflow_lite_calibration_wrapper.pyi',
   'DATA'),
  ('tensorflow\\python\\_pywrap_mlir.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_mlir.pyi',
   'DATA'),
  ('tensorflow\\python\\grappler\\_pywrap_tf_item.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\grappler\\_pywrap_tf_item.pyi',
   'DATA'),
  ('tensorflow\\python\\_pywrap_sanitizers.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_sanitizers.pyi',
   'DATA'),
  ('tensorflow\\python\\saved_model\\pywrap_saved_model\\metrics.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\saved_model\\pywrap_saved_model\\metrics.pyi',
   'DATA'),
  ('tensorflow\\python\\framework\\_python_memory_checker_helper.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_python_memory_checker_helper.pyi',
   'DATA'),
  ('tensorflow\\python\\tpu\\_pywrap_tpu_embedding.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\tpu\\_pywrap_tpu_embedding.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\pywrap_xla_ops.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\pywrap_xla_ops.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_transform_graph.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_transform_graph.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_stat_summarizer.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_stat_summarizer.pyi',
   'DATA'),
  ('tensorflow\\python\\framework\\_test_metrics_util.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_test_metrics_util.pyi',
   'DATA'),
  ('tensorflow\\python\\data\\experimental\\service\\_pywrap_snapshot_utils.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\data\\experimental\\service\\_pywrap_snapshot_utils.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_tensor_float_32_execution.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_tensor_float_32_execution.pyi',
   'DATA'),
  ('tensorflow\\python\\saved_model\\pywrap_saved_model\\fingerprinting.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\saved_model\\pywrap_saved_model\\fingerprinting.pyi',
   'DATA'),
  ('tensorflow\\python\\tpu\\_pywrap_sparse_core_layout.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\tpu\\_pywrap_sparse_core_layout.pyi',
   'DATA'),
  ('tensorflow\\python\\profiler\\internal\\_pywrap_traceme.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\profiler\\internal\\_pywrap_traceme.pyi',
   'DATA'),
  ('tensorflow\\python\\profiler\\internal\\_pywrap_profiler_plugin.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\profiler\\internal\\_pywrap_profiler_plugin.pyi',
   'DATA'),
  ('tensorflow\\python\\_pywrap_tfe.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_tfe.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_kernel_registry.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_kernel_registry.pyi',
   'DATA'),
  ('tensorflow\\compiler\\mlir\\lite\\python\\_pywrap_converter_api.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\mlir\\lite\\python\\_pywrap_converter_api.pyi',
   'DATA'),
  ('tensorflow\\python\\lib\\io\\_pywrap_record_io.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\lib\\io\\_pywrap_record_io.pyi',
   'DATA'),
  ('tensorflow\\python\\framework\\_pywrap_python_op_gen.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_pywrap_python_op_gen.pyi',
   'DATA'),
  ('tensorflow\\lite\\python\\metrics\\_pywrap_tensorflow_lite_metrics_wrapper.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\lite\\python\\metrics\\_pywrap_tensorflow_lite_metrics_wrapper.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_determinism.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_determinism.pyi',
   'DATA'),
  ('tensorflow\\compiler\\tf2tensorrt\\_pywrap_py_utils.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\tf2tensorrt\\_pywrap_py_utils.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_utils.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_utils.pyi',
   'DATA'),
  ('tensorflow\\python\\_pywrap_tfcompile.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_tfcompile.pyi',
   'DATA'),
  ('tensorflow\\python\\profiler\\internal\\_pywrap_profiler.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\profiler\\internal\\_pywrap_profiler.pyi',
   'DATA'),
  ('tensorflow\\compiler\\mlir\\quantization\\tensorflow\\python\\pywrap_function_lib.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\mlir\\quantization\\tensorflow\\python\\pywrap_function_lib.pyi',
   'DATA'),
  ('tensorflow\\python\\framework\\_proto_comparators.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_proto_comparators.pyi',
   'DATA'),
  ('tensorflow\\python\\framework\\_dtypes.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_dtypes.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\fast_module_type.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\fast_module_type.pyi',
   'DATA'),
  ('tensorflow\\python\\saved_model\\pywrap_saved_model\\__init__.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\saved_model\\pywrap_saved_model\\__init__.pyi',
   'DATA'),
  ('tensorflow\\python\\grappler\\_pywrap_tf_optimizer.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\grappler\\_pywrap_tf_optimizer.pyi',
   'DATA'),
  ('tensorflow\\python\\framework\\_pywrap_python_api_dispatcher.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\framework\\_pywrap_python_api_dispatcher.pyi',
   'DATA'),
  ('tensorflow\\python\\client\\_pywrap_events_writer.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\client\\_pywrap_events_writer.pyi',
   'DATA'),
  ('tensorflow\\python\\_pywrap_quantize_training.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_quantize_training.pyi',
   'DATA'),
  ('tensorflow\\python\\util\\_pywrap_tfprof.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\util\\_pywrap_tfprof.pyi',
   'DATA'),
  ('tensorflow\\python\\client\\_pywrap_tf_session.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\client\\_pywrap_tf_session.pyi',
   'DATA'),
  ('tensorflow\\compiler\\mlir\\tensorflow_to_stablehlo\\python\\pywrap_tensorflow_to_stablehlo.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\compiler\\mlir\\tensorflow_to_stablehlo\\python\\pywrap_tensorflow_to_stablehlo.pyi',
   'DATA'),
  ('tensorflow\\python\\_pywrap_parallel_device.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\_pywrap_parallel_device.pyi',
   'DATA'),
  ('grpc\\_cython\\_credentials\\roots.pem',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\grpc\\_cython\\_credentials\\roots.pem',
   'DATA'),
  ('_soundfile_data\\COPYING',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\_soundfile_data\\COPYING',
   'DATA'),
  ('librosa\\feature\\__init__.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\librosa\\feature\\__init__.pyi',
   'DATA'),
  ('librosa\\__init__.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\librosa\\__init__.pyi',
   'DATA'),
  ('librosa\\util\\example_data\\registry.txt',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\librosa\\util\\example_data\\registry.txt',
   'DATA'),
  ('librosa\\core\\__init__.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\librosa\\core\\__init__.pyi',
   'DATA'),
  ('librosa\\core\\intervals.msgpack',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\librosa\\core\\intervals.msgpack',
   'DATA'),
  ('librosa\\py.typed',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\librosa\\py.typed',
   'DATA'),
  ('librosa\\util\\example_data\\index.json',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\librosa\\util\\example_data\\index.json',
   'DATA'),
  ('librosa\\util\\__init__.pyi',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\librosa\\util\\__init__.pyi',
   'DATA'),
  ('tensorflow\\python\\autograph\\impl\\api.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\impl\\api.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\utils\\ag_logging.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\utils\\ag_logging.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\static_analysis\\reaching_definitions.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\static_analysis\\reaching_definitions.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\transformer.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\transformer.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\templates.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\templates.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\ast_util.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\ast_util.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\pretty_printer.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\pretty_printer.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\parser.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\parser.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\static_analysis\\activity.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\static_analysis\\activity.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\static_analysis\\annos.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\static_analysis\\annos.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\static_analysis\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\static_analysis\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\static_analysis\\reaching_fndefs.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\static_analysis\\reaching_fndefs.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\static_analysis\\liveness.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\static_analysis\\liveness.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\transpiler.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\transpiler.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\naming.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\naming.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\loader.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\loader.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\cache.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\cache.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\qual_names.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\qual_names.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\origin_info.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\origin_info.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\inspect_utils.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\inspect_utils.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\errors.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\errors.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\error_utils.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\error_utils.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\cfg.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\cfg.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\anno.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\anno.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\gast_util.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\gast_util.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\operators\\py_builtins.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\operators\\py_builtins.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\utils\\type_registry.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\utils\\type_registry.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\utils\\tensors.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\utils\\tensors.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\lang\\special_functions.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\lang\\special_functions.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\operators\\data_structures.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\operators\\data_structures.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\lang\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\lang\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\lang\\directives.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\lang\\directives.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\impl\\conversion.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\impl\\conversion.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\core\\config.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\core\\config.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\core\\config_lib.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\core\\config_lib.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\core\\unsupported_features_checker.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\core\\unsupported_features_checker.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\core\\function_wrappers.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\core\\function_wrappers.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\operators\\variables.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\operators\\variables.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\variables.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\variables.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\slices.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\slices.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\return_statements.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\return_statements.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\logical_expressions.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\logical_expressions.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\lists.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\lists.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\functions.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\functions.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\directives.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\directives.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\control_flow.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\control_flow.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\continue_statements.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\continue_statements.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\conditional_expressions.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\conditional_expressions.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\call_trees.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\call_trees.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\break_statements.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\break_statements.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\asserts.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\asserts.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\converters\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\converters\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\utils\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\utils\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\utils\\tensor_list.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\utils\\tensor_list.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\utils\\misc.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\utils\\misc.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\utils\\context_managers.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\utils\\context_managers.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\operators\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\operators\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\operators\\slices.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\operators\\slices.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\operators\\logical.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\operators\\logical.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\operators\\exceptions.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\operators\\exceptions.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\operators\\conditional_expressions.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\operators\\conditional_expressions.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\operators\\control_flow.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\operators\\control_flow.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\impl\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\impl\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\core\\converter.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\core\\converter.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\core\\ag_ctx.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\core\\ag_ctx.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\core\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\core\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\utils\\testing.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\utils\\testing.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\testing\\decorators.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\testing\\decorators.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\testing\\basic_definitions.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\testing\\basic_definitions.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\testing\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\testing\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\static_analysis\\type_inference.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\static_analysis\\type_inference.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\common_transformers\\anf.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\common_transformers\\anf.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\pyct\\common_transformers\\__init__.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\pyct\\common_transformers\\__init__.py',
   'DATA'),
  ('tensorflow\\python\\autograph\\core\\converter_testing.py',
   'D:\\RNN\\rnnoise-main\\training\\env\\python\\Lib\\site-packages\\tensorflow\\python\\autograph\\core\\converter_testing.py',
   'DATA'),
  ('base_library.zip',
   'D:\\RNN\\rnnoise-main\\training\\models_voice_enhanced\\build\\VoiceEnhancedRNNoise\\base_library.zip',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
